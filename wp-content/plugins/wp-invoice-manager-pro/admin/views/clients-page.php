<?php
/**
 * Clients Page Template
 *
 * @package SimpleInvoice
 * @since 1.0.0
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

// Get clients and search parameters
$client_manager = new SI_Client();
$search = isset($_GET['search']) ? sanitize_text_field($_GET['search']) : '';
$paged = isset($_GET['paged']) ? max(1, intval($_GET['paged'])) : 1;
$per_page = 20;
$offset = ($paged - 1) * $per_page;

// Get clients with search
$clients = $client_manager->si_get_clients(array(
    'search' => $search,
    'limit' => $per_page,
    'offset' => $offset
));

// Get total count for pagination
$total_clients = count($client_manager->si_get_clients(array('search' => $search)));
$total_pages = ceil($total_clients / $per_page);

// Get statistics
$all_clients = $client_manager->si_get_clients();
$total_count = count($all_clients);

// Get invoice manager for client statistics
$invoice_manager = new SI_Invoice();
$client_stats = array();
$active_clients = 0;
$total_revenue = 0;

foreach ($all_clients as $client) {
    $client_invoices = $invoice_manager->si_get_invoices(array('client_id' => $client->id));
    $client_revenue = array_sum(array_map(function($inv) { return floatval($inv->total_amount); }, $client_invoices));

    $client_stats[$client->id] = array(
        'total_invoices' => count($client_invoices),
        'total_amount' => $client_revenue,
        'last_invoice' => !empty($client_invoices) ? max(array_map(function($inv) { return strtotime($inv->created_at); }, $client_invoices)) : null
    );

    if (count($client_invoices) > 0) {
        $active_clients++;
    }
    $total_revenue += $client_revenue;
}

// Set up page header variables
$page_title = __('Clients', 'wp-invoice-manager-pro');
$page_subtitle = __('Manage your client relationships and contact information', 'wp-invoice-manager-pro');
$page_icon = 'dashicons-groups';
$header_actions = array(
    array(
        'type' => 'button',
        'text' => __('Add New Client', 'wp-invoice-manager-pro'),
        'icon' => 'dashicons-plus-alt',
        'class' => 'si-btn si-btn-primary',
        'id' => 'si-add-client-btn'
    )
);

// Include common header
include WIMP_PLUGIN_PATH . 'admin/views/common/page-header.php';
?>

    <!-- Statistics Overview -->
    <div class="si-dashboard-stats">
        <h2><?php echo esc_html__('Client Overview', 'simple-invoice'); ?></h2>

        <div class="si-stats-grid">
            <!-- Total Clients -->
            <div class="si-stat-card si-stat-clients">
                <div class="si-stat-icon">
                    <span class="dashicons dashicons-groups"></span>
                </div>
                <div class="si-stat-content">
                    <div class="si-stat-number"><?php echo esc_html($total_count); ?></div>
                    <div class="si-stat-label"><?php echo esc_html__('Total Clients', 'simple-invoice'); ?></div>
                    <div class="si-stat-meta"><?php echo esc_html__('All clients', 'simple-invoice'); ?></div>
                </div>
            </div>

            <!-- Active Clients -->
            <div class="si-stat-card si-stat-active">
                <div class="si-stat-icon">
                    <span class="dashicons dashicons-yes"></span>
                </div>
                <div class="si-stat-content">
                    <div class="si-stat-number"><?php echo esc_html($active_clients); ?></div>
                    <div class="si-stat-label"><?php echo esc_html__('Active Clients', 'simple-invoice'); ?></div>
                    <div class="si-stat-meta"><?php echo esc_html__('With invoices', 'simple-invoice'); ?></div>
                </div>
            </div>

            <!-- Total Revenue -->
            <div class="si-stat-card si-stat-revenue">
                <div class="si-stat-icon">
                    <span class="dashicons dashicons-chart-line"></span>
                </div>
                <div class="si-stat-content">
                    <div class="si-stat-number"><?php echo esc_html(si_format_currency($total_revenue)); ?></div>
                    <div class="si-stat-label"><?php echo esc_html__('Total Revenue', 'simple-invoice'); ?></div>
                    <div class="si-stat-meta"><?php echo esc_html__('From all clients', 'simple-invoice'); ?></div>
                </div>
            </div>

            <!-- Average per Client -->
            <div class="si-stat-card si-stat-average">
                <div class="si-stat-icon">
                    <span class="dashicons dashicons-calculator"></span>
                </div>
                <div class="si-stat-content">
                    <div class="si-stat-number"><?php echo esc_html(si_format_currency($active_clients > 0 ? $total_revenue / $active_clients : 0)); ?></div>
                    <div class="si-stat-label"><?php echo esc_html__('Avg per Client', 'simple-invoice'); ?></div>
                    <div class="si-stat-meta"><?php echo esc_html__('Revenue average', 'simple-invoice'); ?></div>
                </div>
            </div>
        </div>
    </div>

    <!-- Search Section -->
    <div class="si-search-section">
        <form method="get" action="">
            <input type="hidden" name="page" value="wimp-clients" />
            <div class="si-search-wrapper">
                <input type="text"
                       name="search"
                       value="<?php echo esc_attr($search); ?>"
                       placeholder="<?php echo esc_attr__('Search clients by name, email, or business...', 'simple-invoice'); ?>"
                       class="si-search-input" />
                <button type="submit" class="si-btn si-btn-secondary">
                    <span class="dashicons dashicons-search"></span>
                    <?php echo esc_html__('Search', 'simple-invoice'); ?>
                </button>
                <?php if (!empty($search)): ?>
                    <a href="<?php echo esc_url(admin_url('admin.php?page=wimp-clients')); ?>" class="si-btn si-btn-ghost">
                        <span class="dashicons dashicons-no"></span>
                        <?php echo esc_html__('Clear', 'simple-invoice'); ?>
                    </a>
                <?php endif; ?>
            </div>
        </form>
    </div>

    <!-- Clients Table -->
    <div class="si-clients-table-section">
        <?php if (!empty($clients)): ?>
            <div class="si-table-container">
                <table class="si-table si-clients-table">
                    <thead>
                        <tr>
                            <th><?php echo esc_html__('Client', 'simple-invoice'); ?></th>
                            <th><?php echo esc_html__('Contact', 'simple-invoice'); ?></th>
                            <th><?php echo esc_html__('Invoices', 'simple-invoice'); ?></th>
                            <th><?php echo esc_html__('Revenue', 'simple-invoice'); ?></th>
                            <th><?php echo esc_html__('Last Invoice', 'simple-invoice'); ?></th>
                            <th><?php echo esc_html__('Actions', 'simple-invoice'); ?></th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php foreach ($clients as $client): ?>
                            <?php
                            $stats = $client_stats[$client->id] ?? array('total_invoices' => 0, 'total_amount' => 0, 'last_invoice' => null);
                            ?>
                            <tr>
                                <td>
                                    <div class="si-client-info">
                                        <div class="si-client-name">
                                            <strong><?php echo esc_html($client->name); ?></strong>
                                        </div>
                                        <?php if (!empty($client->business_name)): ?>
                                            <div class="si-client-business">
                                                <?php echo esc_html($client->business_name); ?>
                                            </div>
                                        <?php endif; ?>
                                    </div>
                                </td>
                                <td>
                                    <div class="si-client-contact">
                                        <?php if (!empty($client->email)): ?>
                                            <div class="si-client-email">
                                                <span class="dashicons dashicons-email"></span>
                                                <a href="mailto:<?php echo esc_attr($client->email); ?>"><?php echo esc_html($client->email); ?></a>
                                            </div>
                                        <?php endif; ?>
                                        <?php if (!empty($client->phone)): ?>
                                            <div class="si-client-phone">
                                                <span class="dashicons dashicons-phone"></span>
                                                <?php echo esc_html($client->phone); ?>
                                            </div>
                                        <?php endif; ?>
                                    </div>
                                </td>
                                <td>
                                    <span class="si-invoice-count"><?php echo esc_html($stats['total_invoices']); ?></span>
                                </td>
                                <td>
                                    <span class="si-revenue-amount"><?php echo esc_html(si_format_currency($stats['total_amount'])); ?></span>
                                </td>
                                <td>
                                    <?php if ($stats['last_invoice']): ?>
                                        <span class="si-last-invoice"><?php echo esc_html(date('M j, Y', $stats['last_invoice'])); ?></span>
                                    <?php else: ?>
                                        <span class="si-no-invoices"><?php echo esc_html__('No invoices', 'simple-invoice'); ?></span>
                                    <?php endif; ?>
                                </td>
                                <td>
                                    <div class="si-client-actions">
                                        <button type="button" class="si-btn si-btn-sm si-btn-secondary si-edit-client" data-client-id="<?php echo esc_attr($client->id); ?>">
                                            <span class="dashicons dashicons-edit"></span>
                                            <?php echo esc_html__('Edit', 'simple-invoice'); ?>
                                        </button>
                                        <a href="<?php echo esc_url(admin_url('admin.php?page=wimp-create-invoice&client_id=' . $client->id)); ?>" class="si-btn si-btn-sm si-btn-primary">
                                            <span class="dashicons dashicons-plus-alt"></span>
                                            <?php echo esc_html__('Invoice', 'simple-invoice'); ?>
                                        </a>
                                        <button type="button" class="si-btn si-btn-sm si-btn-danger si-delete-client" data-client-id="<?php echo esc_attr($client->id); ?>">
                                            <span class="dashicons dashicons-trash"></span>
                                        </button>
                                    </div>
                                </td>
                            </tr>
                        <?php endforeach; ?>
                    </tbody>
                </table>
            </div>

            <!-- Pagination -->
            <?php if ($total_pages > 1): ?>
                <div class="si-pagination">
                    <?php
                    $pagination_args = array(
                        'base' => add_query_arg('paged', '%#%'),
                        'format' => '',
                        'current' => $paged,
                        'total' => $total_pages,
                        'prev_text' => '&laquo; ' . __('Previous', 'simple-invoice'),
                        'next_text' => __('Next', 'simple-invoice') . ' &raquo;'
                    );
                    echo paginate_links($pagination_args);
                    ?>
                </div>
            <?php endif; ?>

        <?php else: ?>
            <!-- Empty State -->
            <div class="si-empty-state">
                <div class="si-empty-icon">
                    <span class="dashicons dashicons-groups"></span>
                </div>
                <h3><?php echo esc_html__('No Clients Found', 'simple-invoice'); ?></h3>
                <?php if (!empty($search)): ?>
                    <p><?php echo esc_html__('No clients match your search criteria. Try a different search term or add a new client.', 'simple-invoice'); ?></p>
                    <a href="<?php echo esc_url(admin_url('admin.php?page=wimp-clients')); ?>" class="si-btn si-btn-secondary">
                        <span class="dashicons dashicons-arrow-left-alt"></span>
                        <?php echo esc_html__('View All Clients', 'simple-invoice'); ?>
                    </a>
                <?php else: ?>
                    <p><?php echo esc_html__('Start building your client database by adding your first client. Clients are essential for creating and managing invoices.', 'simple-invoice'); ?></p>
                <?php endif; ?>
                <button type="button" class="si-btn si-btn-primary" id="si-add-first-client">
                    <span class="dashicons dashicons-plus-alt"></span>
                    <?php echo esc_html__('Add Your First Client', 'simple-invoice'); ?>
                </button>
            </div>
        <?php endif; ?>
    </div>

<!-- Add/Edit Client Modal -->
<div id="si-client-modal" class="si-modal" style="display: none;">
    <div class="si-modal-content">
        <div class="si-modal-header">
            <h2 id="si-client-modal-title"><?php echo esc_html__('Add New Client', 'simple-invoice'); ?></h2>
            <button type="button" class="si-modal-close">&times;</button>
        </div>

        <div class="si-modal-body">
            <form id="si-client-form">
                <input type="hidden" id="si-client-id" name="client_id" value="" />

                <div class="si-form-section">
                    <h3><?php echo esc_html__('Basic Information', 'simple-invoice'); ?></h3>
                    <table class="form-table">
                        <tr>
                            <th scope="row">
                                <label for="si-client-name"><?php echo esc_html__('Full Name', 'simple-invoice'); ?> <span class="required">*</span></label>
                            </th>
                            <td>
                                <input type="text"
                                       id="si-client-name"
                                       name="name"
                                       class="regular-text"
                                       required
                                       placeholder="<?php echo esc_attr__('Client full name', 'simple-invoice'); ?>" />
                            </td>
                        </tr>

                        <tr>
                            <th scope="row">
                                <label for="si-client-business"><?php echo esc_html__('Business Name', 'simple-invoice'); ?></label>
                            </th>
                            <td>
                                <input type="text"
                                       id="si-client-business"
                                       name="business_name"
                                       class="regular-text"
                                       placeholder="<?php echo esc_attr__('Business or company name', 'simple-invoice'); ?>" />
                            </td>
                        </tr>

                        <tr>
                            <th scope="row">
                                <label for="si-client-email"><?php echo esc_html__('Email Address', 'simple-invoice'); ?></label>
                            </th>
                            <td>
                                <input type="email"
                                       id="si-client-email"
                                       name="email"
                                       class="regular-text"
                                       placeholder="<?php echo esc_attr__('<EMAIL>', 'simple-invoice'); ?>" />
                            </td>
                        </tr>

                        <tr>
                            <th scope="row">
                                <label for="si-client-phone"><?php echo esc_html__('Phone Number', 'simple-invoice'); ?></label>
                            </th>
                            <td>
                                <input type="text"
                                       id="si-client-phone"
                                       name="phone"
                                       class="regular-text"
                                       placeholder="<?php echo esc_attr__('+****************', 'simple-invoice'); ?>" />
                            </td>
                        </tr>

                        <tr>
                            <th scope="row">
                                <label for="si-client-address"><?php echo esc_html__('Address', 'simple-invoice'); ?></label>
                            </th>
                            <td>
                                <textarea id="si-client-address"
                                          name="address"
                                          rows="3"
                                          class="large-text"
                                          placeholder="<?php echo esc_attr__('Client address', 'simple-invoice'); ?>"></textarea>
                            </td>
                        </tr>
                    </table>
                </div>
            </form>
        </div>

        <div class="si-modal-footer">
            <button type="button" class="button button-secondary si-modal-close"><?php echo esc_html__('Cancel', 'simple-invoice'); ?></button>
            <button type="button" class="button button-primary" id="si-save-client"><?php echo esc_html__('Save Client', 'simple-invoice'); ?></button>
        </div>
    </div>
</div>

<script>
jQuery(document).ready(function($) {
    var clientModal = $('#si-client-modal');
    var clientForm = $('#si-client-form');
    var isEditing = false;

    // Open add client modal
    $('#si-add-client-btn, #si-add-first-client').on('click', function(e) {
        e.preventDefault();
        openClientModal();
    });

    // Open edit client modal
    $(document).on('click', '.si-edit-client', function() {
        var clientId = $(this).data('client-id');
        openClientModal(clientId);
    });

    // Delete client
    $(document).on('click', '.si-delete-client', function() {
        var clientId = $(this).data('client-id');
        var clientRow = $(this).closest('tr');
        var clientName = clientRow.find('.si-client-name strong').text();

        if (confirm('<?php echo esc_js(__('Are you sure you want to delete', 'simple-invoice')); ?> "' + clientName + '"? <?php echo esc_js(__('This action cannot be undone.', 'simple-invoice')); ?>')) {
            deleteClient(clientId, clientRow);
        }
    });

    // Close modal
    $('.si-modal-close').on('click', function() {
        closeClientModal();
    });

    // Close modal when clicking backdrop
    $(document).on('click', '.si-modal', function(e) {
        if (e.target === this) {
            closeClientModal();
        }
    });

    // Save client
    $('#si-save-client').on('click', function() {
        saveClient();
    });

    function openClientModal(clientId) {
        isEditing = !!clientId;

        if (isEditing) {
            $('#si-client-modal-title').text('<?php echo esc_js(__('Edit Client', 'simple-invoice')); ?>');
            $('#si-save-client').text('<?php echo esc_js(__('Update Client', 'simple-invoice')); ?>');
            loadClientData(clientId);
        } else {
            $('#si-client-modal-title').text('<?php echo esc_js(__('Add New Client', 'simple-invoice')); ?>');
            $('#si-save-client').text('<?php echo esc_js(__('Save Client', 'simple-invoice')); ?>');
            clientForm[0].reset();
            $('#si-client-id').val('');
        }

        clientModal.show();
    }

    function closeClientModal() {
        clientModal.hide();
        clientForm[0].reset();
        isEditing = false;
    }

    function loadClientData(clientId) {
        $.ajax({
            url: ajaxurl,
            type: 'POST',
            data: {
                action: 'si_get_client',
                client_id: clientId,
                nonce: '<?php echo wp_create_nonce('si_get_client_nonce'); ?>'
            },
            success: function(response) {
                if (response.success && response.data) {
                    var client = response.data;
                    $('#si-client-id').val(client.id || '');
                    $('#si-client-name').val(client.name || '');
                    $('#si-client-business').val(client.business_name || '');
                    $('#si-client-email').val(client.email || '');
                    $('#si-client-phone').val(client.phone || '');
                    $('#si-client-address').val(client.address || '');
                } else {
                    alert('<?php echo esc_js(__('Failed to load client data. Please try again.', 'simple-invoice')); ?>');
                }
            },
            error: function() {
                alert('<?php echo esc_js(__('Error loading client data. Please try again.', 'simple-invoice')); ?>');
            }
        });
    }

    function saveClient() {
        var formData = {
            action: isEditing ? 'si_update_client' : 'si_add_client',
            nonce: '<?php echo wp_create_nonce('si_save_client_nonce'); ?>',
            client_id: $('#si-client-id').val(),
            name: $('#si-client-name').val(),
            business_name: $('#si-client-business').val(),
            email: $('#si-client-email').val(),
            phone: $('#si-client-phone').val(),
            address: $('#si-client-address').val()
        };

        // Validate required fields
        if (!formData.name.trim()) {
            alert('<?php echo esc_js(__('Please enter the client name.', 'simple-invoice')); ?>');
            $('#si-client-name').focus();
            return;
        }

        $.ajax({
            url: ajaxurl,
            type: 'POST',
            data: formData,
            success: function(response) {
                if (response.success) {
                    closeClientModal();
                    location.reload(); // Reload to show updated data
                } else {
                    alert(response.data || '<?php echo esc_js(__('Failed to save client. Please try again.', 'simple-invoice')); ?>');
                }
            },
            error: function() {
                alert('<?php echo esc_js(__('Error saving client. Please try again.', 'simple-invoice')); ?>');
            }
        });
    }

    function deleteClient(clientId, clientRow) {
        clientRow.css('opacity', '0.5');

        $.ajax({
            url: ajaxurl,
            type: 'POST',
            data: {
                action: 'si_delete_client',
                client_id: clientId,
                nonce: '<?php echo wp_create_nonce('si_delete_client_nonce'); ?>'
            },
            success: function(response) {
                if (response.success) {
                    clientRow.fadeOut(300, function() {
                        $(this).remove();
                        if ($('.si-clients-table tbody tr').length === 0) {
                            location.reload(); // Reload to show empty state
                        }
                    });
                } else {
                    clientRow.css('opacity', '1');
                    alert(response.data || '<?php echo esc_js(__('Failed to delete client. Please try again.', 'simple-invoice')); ?>');
                }
            },
            error: function() {
                clientRow.css('opacity', '1');
                alert('<?php echo esc_js(__('Error deleting client. Please try again.', 'simple-invoice')); ?>');
            }
        });
    }
});
</script>

<?php
// Include common footer
include WIMP_PLUGIN_PATH . 'admin/views/common/page-footer.php';
?>
