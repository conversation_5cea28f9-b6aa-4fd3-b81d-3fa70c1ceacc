<?php
/**
 * Invoices List Page Template
 *
 * @package SimpleInvoice
 * @since 1.0.0
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

// Get invoices with pagination and search
$invoice_manager = new SI_Invoice();
$client_manager = new SI_Client();

// Handle search and pagination
$search = isset($_GET['search']) ? sanitize_text_field($_GET['search']) : '';
$status_filter = isset($_GET['status']) ? sanitize_text_field($_GET['status']) : '';
$date_from = isset($_GET['date_from']) ? sanitize_text_field($_GET['date_from']) : '';
$date_to = isset($_GET['date_to']) ? sanitize_text_field($_GET['date_to']) : '';
$paged = isset($_GET['paged']) ? max(1, intval($_GET['paged'])) : 1;
$per_page = 20;
$offset = ($paged - 1) * $per_page;

// Get invoices with filters
$invoices = $invoice_manager->si_get_invoices_with_filters($search, $status_filter, $date_from, $date_to, $per_page, $offset);
$total_invoices = $invoice_manager->si_count_invoices_with_filters($search, $status_filter, $date_from, $date_to);
$total_pages = ceil($total_invoices / $per_page);

// Get all clients for the filter dropdown
$clients = $client_manager->si_get_clients();

// Set up page header variables for common header
$page_title = __('Invoices', 'wp-invoice-manager-pro');
$page_subtitle = __('Manage and track all your invoices', 'wp-invoice-manager-pro');
$page_icon = 'dashicons-media-text';
$header_actions = array(
    array(
        'type' => 'link',
        'url' => admin_url('admin.php?page=wimp-create-invoice'),
        'text' => __('Add New Invoice', 'wp-invoice-manager-pro'),
        'icon' => 'dashicons-plus-alt',
        'class' => 'si-btn si-btn-primary'
    )
);
$container_class = 'si-invoices-page';

// Include common header
include WIMP_PLUGIN_PATH . 'admin/views/common/page-header.php';
?>

    <!-- Quick Stats -->
    <div class="si-quick-stats">
        <?php
        $stats = array(
            'total' => $invoice_manager->si_count_invoices_with_filters('', ''),
            'draft' => $invoice_manager->si_count_invoices_with_filters('', 'draft'),
            'sent' => $invoice_manager->si_count_invoices_with_filters('', 'sent'),
            'paid' => $invoice_manager->si_count_invoices_with_filters('', 'paid'),
            'overdue' => $invoice_manager->si_count_invoices_with_filters('', 'overdue')
        );
        ?>

        <div class="si-stat-item">
            <div class="si-stat-icon si-stat-blue">
                <span class="dashicons dashicons-media-text"></span>
            </div>
            <div class="si-stat-info">
                <span class="si-stat-number"><?php echo esc_html($stats['total']); ?></span>
                <span class="si-stat-label"><?php echo esc_html__('Total Invoices', 'simple-invoice'); ?></span>
            </div>
        </div>

        <div class="si-stat-item">
            <div class="si-stat-icon si-stat-green">
                <span class="dashicons dashicons-yes-alt"></span>
            </div>
            <div class="si-stat-info">
                <span class="si-stat-number"><?php echo esc_html($stats['paid']); ?></span>
                <span class="si-stat-label"><?php echo esc_html__('Paid', 'simple-invoice'); ?></span>
            </div>
        </div>

        <div class="si-stat-item">
            <div class="si-stat-icon si-stat-orange">
                <span class="dashicons dashicons-email-alt"></span>
            </div>
            <div class="si-stat-info">
                <span class="si-stat-number"><?php echo esc_html($stats['sent']); ?></span>
                <span class="si-stat-label"><?php echo esc_html__('Sent', 'simple-invoice'); ?></span>
            </div>
        </div>

        <div class="si-stat-item">
            <div class="si-stat-icon si-stat-purple">
                <span class="dashicons dashicons-edit"></span>
            </div>
            <div class="si-stat-info">
                <span class="si-stat-number"><?php echo esc_html($stats['draft']); ?></span>
                <span class="si-stat-label"><?php echo esc_html__('Draft', 'simple-invoice'); ?></span>
            </div>
        </div>
    </div>

    <!-- Search and Filter Section -->
    <div class="si-invoices-content">
        <div class="si-search-section">
            <form method="get" action="" class="si-search-form">
                <input type="hidden" name="page" value="si-invoices" />

                <div class="si-filter-row">
                    <div class="si-search-input-wrapper">
                        <span class="dashicons dashicons-search si-search-icon"></span>
                        <input type="search"
                               name="search"
                               id="si-invoice-search"
                               value="<?php echo esc_attr($search); ?>"
                               placeholder="<?php echo esc_attr__('Search invoices by number, client name...', 'simple-invoice'); ?>"
                               class="si-search-input" />
                        <?php if ($search): ?>
                            <a href="<?php echo esc_url(admin_url('admin.php?page=wimp-invoices')); ?>" class="si-search-clear">
                                <span class="dashicons dashicons-dismiss"></span>
                            </a>
                        <?php endif; ?>
                    </div>

                    <div class="si-filter-group">
                        <select name="status" id="si-status-filter" class="si-filter-select">
                            <option value=""><?php echo esc_html__('All Statuses', 'simple-invoice'); ?></option>
                            <option value="draft" <?php selected($status_filter, 'draft'); ?>><?php echo esc_html__('Draft', 'simple-invoice'); ?></option>
                            <option value="sent" <?php selected($status_filter, 'sent'); ?>><?php echo esc_html__('Sent', 'simple-invoice'); ?></option>
                            <option value="paid" <?php selected($status_filter, 'paid'); ?>><?php echo esc_html__('Paid', 'simple-invoice'); ?></option>
                            <option value="overdue" <?php selected($status_filter, 'overdue'); ?>><?php echo esc_html__('Overdue', 'simple-invoice'); ?></option>
                        </select>

                        <input type="date"
                               name="date_from"
                               id="si-date-from"
                               value="<?php echo esc_attr($date_from); ?>"
                               class="si-filter-date"
                               placeholder="<?php echo esc_attr__('From Date', 'simple-invoice'); ?>" />

                        <input type="date"
                               name="date_to"
                               id="si-date-to"
                               value="<?php echo esc_attr($date_to); ?>"
                               class="si-filter-date"
                               placeholder="<?php echo esc_attr__('To Date', 'simple-invoice'); ?>" />

                        <button type="submit" class="si-btn si-btn-primary si-filter-btn">
                            <span class="dashicons dashicons-filter"></span>
                            <?php echo esc_html__('Filter', 'simple-invoice'); ?>
                        </button>

                        <?php if ($search || $status_filter || $date_from || $date_to): ?>
                            <a href="<?php echo esc_url(admin_url('admin.php?page=wimp-invoices')); ?>" class="si-btn si-btn-secondary">
                                <span class="dashicons dashicons-dismiss"></span>
                                <?php echo esc_html__('Clear', 'simple-invoice'); ?>
                            </a>
                        <?php endif; ?>
                    </div>
                </div>

                <?php if ($search || $status_filter || $date_from || $date_to): ?>
                    <div class="si-filter-results-info">
                        <span class="si-results-text">
                            <?php
                            $showing_text = sprintf(
                                __('Showing %d of %d invoices', 'simple-invoice'),
                                count($invoices),
                                $total_invoices
                            );
                            if ($search) {
                                $showing_text .= sprintf(__(' matching "%s"', 'simple-invoice'), esc_html($search));
                            }
                            if ($status_filter) {
                                $showing_text .= sprintf(__(' with status "%s"', 'simple-invoice'), esc_html(ucfirst($status_filter)));
                            }
                            if ($date_from && $date_to) {
                                $showing_text .= sprintf(__(' from %s to %s', 'simple-invoice'), esc_html(date('M j, Y', strtotime($date_from))), esc_html(date('M j, Y', strtotime($date_to))));
                            } elseif ($date_from) {
                                $showing_text .= sprintf(__(' from %s', 'simple-invoice'), esc_html(date('M j, Y', strtotime($date_from))));
                            } elseif ($date_to) {
                                $showing_text .= sprintf(__(' until %s', 'simple-invoice'), esc_html(date('M j, Y', strtotime($date_to))));
                            }
                            echo esc_html($showing_text);
                            ?>
                        </span>
                    </div>
                <?php endif; ?>
            </form>
        </div>
        <!-- Invoices List -->
        <div class="si-invoices-list-wrapper">
            <?php if (!empty($invoices)): ?>
                <div class="si-invoices-list">
                    <div class="si-list-header">
                        <h3><?php echo esc_html__('Invoice List', 'simple-invoice'); ?></h3>
                        <div class="si-list-info">
                            <?php if (!($search || $status_filter || $date_from || $date_to)): ?>
                                <?php echo esc_html(sprintf(__('Showing %d invoices', 'simple-invoice'), count($invoices))); ?>
                            <?php endif; ?>
                        </div>
                    </div>

                        <?php foreach ($invoices as $index => $invoice): ?>
                            <?php
                            // Calculate serial number based on pagination
                            $serial_number = $offset + $index + 1;

                            // Get client info
                            $client = $client_manager->si_get_client($invoice->client_id);
                            $client_name = $client ? $client->name : __('Unknown Client', 'simple-invoice');

                            // Format date
                            $invoice_date = date('M j, Y', strtotime($invoice->created_at));
                            $invoice_time = date('g:i A', strtotime($invoice->created_at));

                            // Format amount
                            $amount = si_format_currency($invoice->total_amount);

                            // Status badge
                            $status_class = 'si-status-' . $invoice->status;
                            $status_label = ucfirst($invoice->status);

                            // Get items count
                            $invoice_data = json_decode($invoice->invoice_data, true);
                            $items_count = isset($invoice_data['items']) ? count($invoice_data['items']) : 0;
                            ?>
                            <div class="si-invoice-item" data-invoice-id="<?php echo esc_attr($invoice->id); ?>">
                                <div class="si-invoice-row">
                                    <!-- Invoice Info -->
                                    <div class="si-invoice-main">
                                        <div class="si-invoice-number-section">
                                            <div class="si-serial-badge"><?php echo esc_html($serial_number); ?></div>
                                            <div class="si-invoice-details">
                                                <h4 class="si-invoice-number">
                                                    <a href="<?php echo esc_url(wp_nonce_url(
                                                        add_query_arg(array(
                                                            'si_download_pdf' => '1',
                                                            'invoice_id' => $invoice->id
                                                        ), admin_url()),
                                                        'si_download_pdf',
                                                        'nonce'
                                                    )); ?>" target="_blank" class="si-invoice-link">
                                                        <?php echo esc_html($invoice->invoice_number); ?>
                                                    </a>
                                                </h4>
                                                <div class="si-invoice-meta">
                                                    <span class="si-invoice-id">ID: <?php echo esc_html($invoice->id); ?></span>
                                                    <span class="si-separator">•</span>
                                                    <span class="si-invoice-date"><?php echo esc_html($invoice_date); ?></span>
                                                    <span class="si-separator">•</span>
                                                    <span class="si-invoice-time"><?php echo esc_html($invoice_time); ?></span>
                                                </div>
                                            </div>
                                        </div>
                                    </div>

                                    <!-- Client Info -->
                                    <div class="si-invoice-client">
                                        <div class="si-client-name"><?php echo esc_html($client_name); ?></div>
                                        <?php if ($client && !empty($client->business_name)): ?>
                                            <div class="si-client-business"><?php echo esc_html($client->business_name); ?></div>
                                        <?php endif; ?>
                                        <?php if ($client && !empty($client->email)): ?>
                                            <div class="si-client-email">
                                                <span class="dashicons dashicons-email"></span>
                                                <?php echo esc_html($client->email); ?>
                                            </div>
                                        <?php endif; ?>
                                    </div>

                                    <!-- Amount Info -->
                                    <div class="si-invoice-amount">
                                        <div class="si-amount"><?php echo esc_html($amount); ?></div>
                                        <div class="si-amount-meta">
                                            <?php echo esc_html(sprintf(_n('%d item', '%d items', $items_count, 'simple-invoice'), $items_count)); ?>
                                        </div>
                                    </div>

                                    <!-- Status -->
                                    <div class="si-invoice-status">
                                        <span class="si-status-badge <?php echo esc_attr($status_class); ?>">
                                            <?php echo esc_html($status_label); ?>
                                        </span>
                                    </div>

                                    <!-- Actions -->
                                    <div class="si-invoice-actions">
                                        <a href="<?php echo esc_url(wp_nonce_url(
                                            add_query_arg(array(
                                                'si_download_pdf' => '1',
                                                'invoice_id' => $invoice->id
                                            ), admin_url()),
                                            'si_download_pdf',
                                            'nonce'
                                        )); ?>"
                                           class="button button-small"
                                           target="_blank"
                                           title="<?php echo esc_attr__('Download PDF', 'simple-invoice'); ?>">
                                            <span class="dashicons dashicons-download"></span>
                                            <?php echo esc_html__('Download', 'simple-invoice'); ?>
                                        </a>

                                        <button type="button"
                                                class="button button-small si-view-invoice"
                                                data-invoice-id="<?php echo esc_attr($invoice->id); ?>"
                                                title="<?php echo esc_attr__('View Invoice', 'simple-invoice'); ?>">
                                            <span class="dashicons dashicons-visibility"></span>
                                            <?php echo esc_html__('View', 'simple-invoice'); ?>
                                        </button>

                                        <div class="si-status-dropdown">
                                            <button type="button" class="button button-small si-status-toggle" data-invoice-id="<?php echo esc_attr($invoice->id); ?>" title="<?php echo esc_attr__('Change Status', 'simple-invoice'); ?>">
                                                <span class="dashicons dashicons-edit"></span>
                                                <?php echo esc_html__('Status', 'simple-invoice'); ?>
                                            </button>
                                            <div class="si-status-menu" style="display: none;">
                                                <a href="#" class="si-status-option" data-status="draft">
                                                    <span class="si-status-badge si-status-draft"></span>
                                                    <?php echo esc_html__('Draft', 'simple-invoice'); ?>
                                                </a>
                                                <a href="#" class="si-status-option" data-status="sent">
                                                    <span class="si-status-badge si-status-sent"></span>
                                                    <?php echo esc_html__('Sent', 'simple-invoice'); ?>
                                                </a>
                                                <a href="#" class="si-status-option" data-status="paid">
                                                    <span class="si-status-badge si-status-paid"></span>
                                                    <?php echo esc_html__('Paid', 'simple-invoice'); ?>
                                                </a>
                                                <a href="#" class="si-status-option" data-status="overdue">
                                                    <span class="si-status-badge si-status-overdue"></span>
                                                    <?php echo esc_html__('Overdue', 'simple-invoice'); ?>
                                                </a>
                                            </div>
                                        </div>

                                        <button type="button"
                                                class="button button-small button-link-delete si-delete-invoice"
                                                data-invoice-id="<?php echo esc_attr($invoice->id); ?>"
                                                title="<?php echo esc_attr__('Delete Invoice', 'simple-invoice'); ?>">
                                            <span class="dashicons dashicons-trash"></span>
                                            <?php echo esc_html__('Delete', 'simple-invoice'); ?>
                                        </button>
                                    </div>
                                </div>
                            </div>
                        <?php endforeach; ?>
                    </div>
            <?php else: ?>
                <!-- Empty State -->
                <div class="si-empty-state">
                    <div class="si-empty-content">
                        <?php if ($search || $status_filter || $date_from || $date_to): ?>
                            <div class="si-empty-icon">
                                <span class="dashicons dashicons-search"></span>
                            </div>
                            <h3 class="si-empty-title"><?php echo esc_html__('No invoices found', 'simple-invoice'); ?></h3>
                            <p class="si-empty-description"><?php echo esc_html__('No invoices match your search criteria. Try adjusting your filters or search terms.', 'simple-invoice'); ?></p>
                            <div class="si-empty-actions">
                                <a href="<?php echo esc_url(admin_url('admin.php?page=wimp-invoices')); ?>" class="si-btn si-btn-secondary">
                                    <span class="dashicons dashicons-dismiss"></span>
                                    <?php echo esc_html__('Clear Filters', 'simple-invoice'); ?>
                                </a>
                                <a href="<?php echo esc_url(admin_url('admin.php?page=wimp-create-invoice')); ?>" class="si-btn si-btn-primary">
                                    <span class="dashicons dashicons-plus-alt"></span>
                                    <?php echo esc_html__('Create New Invoice', 'simple-invoice'); ?>
                                </a>
                            </div>
                        <?php else: ?>
                            <div class="si-empty-icon">
                                <span class="dashicons dashicons-media-text"></span>
                            </div>
                            <h3 class="si-empty-title"><?php echo esc_html__('No invoices yet', 'simple-invoice'); ?></h3>
                            <p class="si-empty-description"><?php echo esc_html__('You haven\'t created any invoices yet. Create your first invoice to get started with billing your clients.', 'simple-invoice'); ?></p>
                            <div class="si-empty-actions">
                                <a href="<?php echo esc_url(admin_url('admin.php?page=wimp-create-invoice')); ?>" class="si-btn si-btn-primary">
                                    <span class="dashicons dashicons-plus-alt"></span>
                                    <?php echo esc_html__('Create Your First Invoice', 'simple-invoice'); ?>
                                </a>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>
            <?php endif; ?>
        </div>
    </div>
    
    <!-- Pagination -->
    <?php if ($total_pages > 1): ?>
        <div class="si-pagination">
            <?php
            $pagination_args = array(
                'base' => add_query_arg('paged', '%#%'),
                'format' => '',
                'prev_text' => __('&laquo; Previous', 'simple-invoice'),
                'next_text' => __('Next &raquo;', 'simple-invoice'),
                'current' => $paged,
                'total' => $total_pages,
                'add_args' => array(
                    'search' => $search,
                    'status' => $status_filter,
                    'date_from' => $date_from,
                    'date_to' => $date_to
                )
            );
            echo paginate_links($pagination_args);
            ?>
        </div>
    <?php endif; ?>
    </div>
</div>

<style>
/* Modern Invoices Page Styles */

/* Quick Stats */
.si-quick-stats {
    max-width: 1200px;
    margin: 0 auto 20px auto;
    padding: 0 20px;
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(180px, 1fr));
    gap: 15px;
}

.si-stat-item {
    background: #ffffff;
    border: 1px solid #e7e7e7;
    border-radius: 12px;
    padding: 20px;
    display: flex;
    align-items: center;
    gap: 15px;
    transition: all 0.3s ease;
    box-shadow: 0 4px 12px rgba(0,0,0,0.1);
}

.si-stat-item:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(0,0,0,0.15);
}

.si-stat-icon {
    width: 50px;
    height: 50px;
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    flex-shrink: 0;
    background: linear-gradient(135deg, #f47a45 0%, #5f5f5f 100%);
}

.si-stat-icon .dashicons {
    font-size: 22px;
    width: 22px;
    height: 22px;
    color: #ffffff;
}

.si-stat-info {
    flex: 1;
}

.si-stat-number {
    display: block;
    font-size: 20px;
    font-weight: 700;
    color: #000000;
    line-height: 1;
    margin-bottom: 3px;
}

.si-stat-label {
    display: block;
    font-size: 14px;
    color: #5f5f5f;
    font-weight: 500;
}

/* Invoices Content */
.si-invoices-content {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
}

/* Search Section */
.si-search-section {
    background: #ffffff;
    border: 1px solid #e7e7e7;
    border-radius: 8px;
    padding: 20px;
    margin-bottom: 25px;
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.si-filter-row {
    display: flex;
    gap: 10px;
    align-items: center;
    flex-wrap: wrap;
}

.si-search-input-wrapper {
    flex: 1;
    position: relative;
    display: flex;
    align-items: center;
    min-width: 250px;
}

.si-search-icon {
    position: absolute;
    left: 12px;
    color: #5f5f5f;
    font-size: 16px;
    width: 16px;
    height: 16px;
    z-index: 2;
}

.si-search-input {
    width: 100%;
    padding: 10px 14px 10px 35px;
    border: 2px solid #e7e7e7;
    border-radius: 6px;
    font-size: 14px;
    background: #ffffff;
    transition: border-color 0.3s ease;
}

.si-search-input:focus {
    outline: none;
    border-color: #f47a45;
    box-shadow: 0 0 0 1px #f47a45;
}

.si-search-clear {
    position: absolute;
    right: 8px;
    color: #999;
    text-decoration: none;
    padding: 2px;
    border-radius: 2px;
}

.si-search-clear:hover {
    color: #f47a45;
    text-decoration: none;
}

.si-filter-group {
    display: flex;
    gap: 8px;
    align-items: center;
    flex-wrap: wrap;
}

.si-filter-select,
.si-filter-date {
    padding: 8px 12px;
    border: 2px solid #e7e7e7;
    border-radius: 6px;
    font-size: 14px;
    background: #ffffff;
    min-width: 130px;
    transition: border-color 0.3s ease;
}

.si-filter-select:focus,
.si-filter-date:focus {
    outline: none;
    border-color: #f47a45;
    box-shadow: 0 0 0 1px #f47a45;
}

.si-filter-results-info {
    margin-top: 15px;
    padding: 10px 15px;
    background: #fff3e0;
    border-radius: 6px;
    border-left: 4px solid #f47a45;
}

.si-results-text {
    font-size: 14px;
    color: #f47a45;
    font-weight: 600;
}

/* Simple Buttons */
.si-btn {
    display: inline-flex;
    align-items: center;
    gap: 5px;
    padding: 6px 12px;
    border: 1px solid #ccc;
    border-radius: 3px;
    font-size: 13px;
    font-weight: 400;
    text-decoration: none;
    cursor: pointer;
    background: #f7f7f7;
    color: #333;
    white-space: nowrap;
    transition: none;
}

.si-btn:hover {
    background: #fafafa;
    border-color: #999;
    color: #333;
    text-decoration: none;
}

.si-btn:active {
    background: #eee;
}

.si-btn-primary {
    background: linear-gradient(135deg, #f47a45 0%, #5f5f5f 100%);
    color: #ffffff;
    border-color: #f47a45;
}

.si-btn-primary:hover {
    background: linear-gradient(135deg, #5f5f5f 0%, #000000 100%);
    color: #ffffff;
    border-color: #5f5f5f;
}

.si-btn-secondary {
    background: linear-gradient(135deg, #e7e7e7 0%, #ffffff 100%);
    color: #000000;
    border-color: #e7e7e7;
}

.si-btn-secondary:hover {
    background: linear-gradient(135deg, #ffffff 0%, #e7e7e7 100%);
    color: #000000;
    border-color: #e7e7e7;
}

.si-btn .dashicons {
    font-size: 14px;
    width: 14px;
    height: 14px;
}

/* Invoices List */
.si-invoices-list {
    background: #ffffff;
    border: 1px solid #e7e7e7;
    border-radius: 8px;
    overflow: hidden;
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.si-list-header {
    padding: 20px 25px;
    border-bottom: 1px solid #e7e7e7;
    background: #e7e7e7;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.si-list-header h3 {
    margin: 0;
    font-size: 18px;
    font-weight: 700;
    color: #000000;
}

.si-list-info {
    font-size: 14px;
    color: #5f5f5f;
    font-weight: 500;
}

/* Invoices List Container */
.si-invoices-list-container {
    background: white;
}

/* Invoice Item */
.si-invoice-item {
    border-bottom: 1px solid #eee;
    transition: background-color 0.2s ease;
}

.si-invoice-item:last-child {
    border-bottom: none;
}

.si-invoice-item:hover {
    background: #f9f9f9;
}

.si-invoice-row {
    display: flex;
    align-items: center;
    padding: 15px 20px;
    gap: 20px;
}

/* Invoice Main Info */
.si-invoice-main {
    display: flex;
    align-items: center;
    gap: 12px;
    flex: 1;
    min-width: 0;
}

.si-invoice-number-section {
    display: flex;
    align-items: center;
    gap: 10px;
}

.si-serial-badge {
    width: 40px;
    height: 40px;
    background: linear-gradient(135deg, #f47a45 0%, #5f5f5f 100%);
    color: #ffffff;
    border-radius: 10px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 14px;
    font-weight: 700;
    flex-shrink: 0;
}

.si-invoice-details {
    flex: 1;
    min-width: 0;
}

.si-invoice-number {
    margin: 0 0 4px 0;
    font-size: 16px;
    font-weight: 600;
    line-height: 1.3;
}

.si-invoice-link {
    color: #f47a45;
    text-decoration: none;
    font-weight: 600;
}

.si-invoice-link:hover {
    text-decoration: underline;
}

.si-invoice-meta {
    display: flex;
    align-items: center;
    gap: 8px;
    flex-wrap: wrap;
    font-size: 12px;
    color: #666;
}

.si-separator {
    color: #ccc;
}

/* Invoice Client */
.si-invoice-client {
    min-width: 180px;
    flex-shrink: 0;
}

.si-client-name {
    font-size: 14px;
    font-weight: 500;
    color: #333;
    margin-bottom: 3px;
}

.si-client-business {
    font-size: 12px;
    color: #666;
    margin-bottom: 3px;
}

.si-client-email {
    display: flex;
    align-items: center;
    gap: 4px;
    font-size: 11px;
    color: #666;
}

.si-client-email .dashicons {
    font-size: 12px;
    width: 12px;
    height: 12px;
    color: #f47a45;
}

/* Invoice Amount */
.si-invoice-amount {
    min-width: 120px;
    text-align: right;
    flex-shrink: 0;
}

.si-amount {
    font-size: 16px;
    font-weight: 600;
    color: #333;
    display: block;
    margin-bottom: 2px;
}

.si-amount-meta {
    font-size: 11px;
    color: #666;
}

/* Invoice Status */
.si-invoice-status {
    min-width: 80px;
    flex-shrink: 0;
}

.si-status-badge {
    display: inline-block;
    padding: 6px 12px;
    border-radius: 6px;
    font-size: 12px;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    border: 1px solid transparent;
}

.si-status-draft {
    background: #e7e7e7;
    color: #5f5f5f;
    border-color: #e7e7e7;
}

.si-status-sent {
    background: #fff3e0;
    color: #f47a45;
    border-color: #f47a45;
}

.si-status-paid {
    background: #e7e7e7;
    color: #000000;
    border-color: #000000;
}

.si-status-overdue {
    background: #fff3e0;
    color: #f47a45;
    border-color: #f47a45;
}

/* Invoice Actions */
.si-invoice-actions {
    display: flex;
    gap: 6px;
    flex-shrink: 0;
    align-items: center;
}

.si-invoice-actions .button {
    padding: 6px 12px;
    font-size: 12px;
    line-height: 1.4;
    min-height: auto;
    display: inline-flex;
    align-items: center;
    gap: 6px;
    white-space: nowrap;
    border-radius: 6px;
    font-weight: 600;
    transition: all 0.3s ease;
    background: linear-gradient(135deg, #e7e7e7 0%, #ffffff 100%);
    color: #000000;
    border: 1px solid #e7e7e7;
}

.si-invoice-actions .button .dashicons {
    font-size: 14px;
    width: 14px;
    height: 14px;
}

.si-invoice-actions .button:hover {
    background: linear-gradient(135deg, #f47a45 0%, #5f5f5f 100%);
    color: #ffffff;
    transform: translateY(-1px);
}

/* Status Dropdown */
.si-status-dropdown {
    position: relative;
    display: inline-block;
}

.si-status-menu {
    position: absolute;
    top: 100%;
    right: 0;
    background: #ffffff;
    border: 1px solid #e7e7e7;
    border-radius: 8px;
    box-shadow: 0 8px 25px rgba(0,0,0,0.15);
    z-index: 1000;
    min-width: 140px;
}

.si-status-option {
    display: flex;
    align-items: center;
    gap: 10px;
    padding: 10px 15px;
    text-decoration: none;
    color: #000000;
    font-size: 13px;
    font-weight: 500;
    border-bottom: 1px solid #e7e7e7;
    transition: all 0.3s ease;
}

.si-status-option:last-child {
    border-bottom: none;
}

.si-status-option:hover {
    background: linear-gradient(135deg, #f47a45 0%, #5f5f5f 100%);
    color: #ffffff;
    text-decoration: none;
}

.si-status-option .si-status-badge {
    margin: 0;
    padding: 2px 4px;
    font-size: 9px;
}

/* Empty State */
.si-empty-state {
    background: #ffffff;
    border-radius: 12px;
    padding: 80px 40px;
    text-align: center;
    border: 1px solid #e7e7e7;
    box-shadow: 0 4px 12px rgba(0,0,0,0.1);
}

.si-empty-content {
    max-width: 400px;
    margin: 0 auto;
}

.si-empty-icon {
    width: 80px;
    height: 80px;
    margin: 0 auto 25px auto;
    background: linear-gradient(135deg, #f47a45 0%, #5f5f5f 100%);
    border-radius: 20px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.si-empty-icon .dashicons {
    font-size: 40px;
    width: 40px;
    height: 40px;
    color: #ffffff;
}

.si-empty-title {
    margin: 0 0 15px 0;
    font-size: 24px;
    font-weight: 700;
    color: #000000;
}

.si-empty-description {
    margin: 0 0 30px 0;
    font-size: 16px;
    color: #5f5f5f;
    line-height: 1.6;
}

.si-empty-actions {
    display: flex;
    gap: 10px;
    justify-content: center;
    flex-wrap: wrap;
}

/* Responsive Design */
@media (max-width: 768px) {
    .si-header-content {
        flex-direction: column;
        text-align: center;
        gap: 15px;
    }

    .si-page-title {
        font-size: 20px;
        justify-content: center;
    }

    .si-quick-stats {
        grid-template-columns: repeat(auto-fit, minmax(140px, 1fr));
        gap: 10px;
    }

    .si-stat-item {
        padding: 12px;
        flex-direction: column;
        text-align: center;
        gap: 8px;
    }

    .si-stat-icon {
        width: 32px;
        height: 32px;
    }

    .si-stat-icon .dashicons {
        font-size: 16px;
        width: 16px;
        height: 16px;
    }

    .si-stat-number {
        font-size: 16px;
    }

    .si-filter-row {
        flex-direction: column;
        align-items: stretch;
        gap: 10px;
    }

    .si-search-input-wrapper {
        min-width: auto;
    }

    .si-filter-group {
        justify-content: center;
    }

    .si-empty-state {
        padding: 40px 20px;
    }

    .si-empty-title {
        font-size: 18px;
    }

    .si-empty-description {
        font-size: 13px;
    }

    .si-empty-actions {
        flex-direction: column;
    }

    /* Mobile Invoice Layout */
    .si-invoice-row {
        flex-direction: column;
        align-items: stretch;
        gap: 12px;
        padding: 15px;
    }

    .si-invoice-main {
        gap: 10px;
    }

    .si-invoice-client {
        min-width: auto;
        padding: 10px;
        background: #f9f9f9;
        border-radius: 4px;
    }

    .si-invoice-amount {
        min-width: auto;
        text-align: center;
        padding: 8px;
        background: #f9f9f9;
        border-radius: 4px;
    }

    .si-invoice-status {
        min-width: auto;
        text-align: center;
    }

    .si-invoice-actions {
        justify-content: center;
        flex-wrap: wrap;
    }

    .si-invoice-actions .button {
        flex: 1;
        min-width: 80px;
        justify-content: center;
    }
}

@media (max-width: 480px) {
    .si-page-header {
        padding: 15px;
    }

    .si-invoices-content {
        padding: 0 15px;
    }

    .si-quick-stats {
        padding: 0 15px;
        grid-template-columns: 1fr 1fr;
        gap: 8px;
    }

    .si-list-header {
        padding: 12px 15px;
        flex-direction: column;
        gap: 8px;
        text-align: center;
    }

    .si-invoice-row {
        padding: 12px;
    }

    .si-invoice-main {
        flex-direction: column;
        align-items: center;
        text-align: center;
        gap: 8px;
    }

    .si-invoice-number-section {
        flex-direction: column;
        gap: 8px;
        text-align: center;
    }

    .si-serial-badge {
        width: 28px;
        height: 28px;
        font-size: 11px;
    }

    .si-invoice-meta {
        justify-content: center;
        flex-wrap: wrap;
    }

    .si-invoice-client {
        padding: 8px;
    }

    .si-invoice-amount {
        padding: 6px;
    }

    .si-invoice-actions {
        gap: 4px;
    }

    .si-invoice-actions .button {
        font-size: 10px;
        padding: 3px 6px;
        min-width: 70px;
    }

    .si-invoice-actions .button .dashicons {
        font-size: 11px;
        width: 11px;
        height: 11px;
    }

    .si-status-menu {
        right: auto;
        left: 0;
    }
}
</style>

<!-- Invoice View Modal -->
<div id="si-invoice-view-modal" class="si-modal si-modal-large" style="display: none;">
    <div class="si-modal-content">
        <div class="si-modal-header">
            <h2><?php echo esc_html__('Invoice Preview', 'simple-invoice'); ?></h2>
            <button type="button" class="si-modal-close">&times;</button>
        </div>
        
        <div class="si-modal-body">
            <div id="si-invoice-view-content">
                <!-- Invoice content will be loaded here -->
            </div>
        </div>
        
        <div class="si-modal-footer">
            <button type="button" class="button button-secondary si-modal-close">
                <?php echo esc_html__('Close', 'simple-invoice'); ?>
            </button>
        </div>
    </div>
</div>

<script>
jQuery(document).ready(function($) {
    // View invoice
    $('.si-view-invoice').on('click', function() {
        var invoiceId = $(this).data('invoice-id');
        viewInvoice(invoiceId);
    });
    
    // Status dropdown toggle
    $('.si-status-toggle').on('click', function(e) {
        e.stopPropagation();
        var menu = $(this).siblings('.si-status-menu');
        $('.si-status-menu').not(menu).hide();
        menu.toggle();
    });
    
    // Hide status menus when clicking outside
    $(document).on('click', function() {
        $('.si-status-menu').hide();
    });
    
    // Status change
    $('.si-status-option').on('click', function(e) {
        e.preventDefault();
        var invoiceId = $(this).closest('.si-status-dropdown').find('.si-status-toggle').data('invoice-id');
        var newStatus = $(this).data('status');
        updateInvoiceStatus(invoiceId, newStatus);
    });
    
    // Delete invoice
    $('.si-delete-invoice').on('click', function() {
        var invoiceId = $(this).data('invoice-id');
        var invoiceNumber = $(this).closest('tr').find('.column-invoice-number strong a').text();
        
        if (confirm('<?php echo esc_js(__('Are you sure you want to delete invoice', 'simple-invoice')); ?> "' + invoiceNumber + '"?')) {
            deleteInvoice(invoiceId);
        }
    });
    
    // Close modal
    $('.si-modal-close').on('click', function() {
        $(this).closest('.si-modal').hide();
    });
    
    function viewInvoice(invoiceId) {
        $('#si-invoice-view-content').html('<div class="si-loading"><?php echo esc_js(__('Loading...', 'simple-invoice')); ?></div>');
        $('#si-invoice-view-modal').show();
        
        $.ajax({
            url: ajaxurl,
            type: 'POST',
            data: {
                action: 'si_view_invoice',
                invoice_id: invoiceId,
                nonce: '<?php echo wp_create_nonce('si_view_invoice_nonce'); ?>'
            },
            success: function(response) {
                if (response.success) {
                    $('#si-invoice-view-content').html(response.data.html_content);
                } else {
                    $('#si-invoice-view-content').html('<p class="si-error">' + (response.data || '<?php echo esc_js(__('Failed to load invoice.', 'simple-invoice')); ?>') + '</p>');
                }
            },
            error: function() {
                $('#si-invoice-view-content').html('<p class="si-error"><?php echo esc_js(__('An error occurred while loading the invoice.', 'simple-invoice')); ?></p>');
            }
        });
    }
    
    function updateInvoiceStatus(invoiceId, newStatus) {
        $.ajax({
            url: ajaxurl,
            type: 'POST',
            data: {
                action: 'si_update_invoice_status',
                invoice_id: invoiceId,
                status: newStatus,
                nonce: '<?php echo wp_create_nonce('si_update_status_nonce'); ?>'
            },
            success: function(response) {
                if (response.success) {
                    location.reload(); // Refresh to show updated status
                } else {
                    alert(response.data || '<?php echo esc_js(__('Failed to update status.', 'simple-invoice')); ?>');
                }
            },
            error: function() {
                alert('<?php echo esc_js(__('An error occurred while updating status.', 'simple-invoice')); ?>');
            }
        });
    }
    
    function deleteInvoice(invoiceId) {
        $.ajax({
            url: ajaxurl,
            type: 'POST',
            data: {
                action: 'si_delete_invoice',
                invoice_id: invoiceId,
                nonce: '<?php echo wp_create_nonce('si_delete_invoice_nonce'); ?>'
            },
            success: function(response) {
                if (response.success) {
                    $('tr[data-invoice-id="' + invoiceId + '"]').fadeOut(function() {
                        $(this).remove();
                        
                        // Check if table is empty
                        if ($('#si-invoices-tbody tr:visible').length === 0) {
                            location.reload();
                        }
                    });
                } else {
                    alert(response.data || '<?php echo esc_js(__('Failed to delete invoice.', 'simple-invoice')); ?>');
                }
            },
            error: function() {
                alert('<?php echo esc_js(__('An error occurred while deleting the invoice.', 'simple-invoice')); ?>');
            }
        });
    }
});
</script>

<?php
// Include common footer
include WIMP_PLUGIN_PATH . 'admin/views/common/page-footer.php';
?>
