<?php
/**
 * Templates Page Template
 *
 * @package SimpleInvoice
 * @since 1.0.0
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

// Set up page header variables
$page_title = __('Invoice Templates', 'wp-invoice-manager-pro');
$page_subtitle = __('Create and manage your invoice templates', 'wp-invoice-manager-pro');
$page_icon = 'dashicons-admin-page';
$header_actions = array(
    array(
        'type' => 'button',
        'text' => __('Add New Template', 'wp-invoice-manager-pro'),
        'icon' => 'dashicons-plus-alt',
        'class' => 'si-btn si-btn-primary si-add-template-btn'
    )
);

// Include common header
include WIMP_PLUGIN_PATH . 'admin/views/common/page-header.php';
?>

    <!-- Statistics Overview -->
    <div class="si-dashboard-stats">
        <h2><?php echo esc_html__('Template Overview', 'simple-invoice'); ?></h2>

        <div class="si-stats-grid">
            <!-- Total Templates -->
            <div class="si-stat-card si-stat-templates">
                <div class="si-stat-icon">
                    <span class="dashicons dashicons-media-text"></span>
                </div>
                <div class="si-stat-content">
                    <div class="si-stat-number"><?php echo esc_html(count($templates)); ?></div>
                    <div class="si-stat-label"><?php echo esc_html__('Templates', 'simple-invoice'); ?></div>
                    <div class="si-stat-meta"><?php echo esc_html__('Total templates', 'simple-invoice'); ?></div>
                </div>
            </div>

            <!-- Available Designs -->
            <div class="si-stat-card si-stat-designs">
                <div class="si-stat-icon">
                    <span class="dashicons dashicons-admin-appearance"></span>
                </div>
                <div class="si-stat-content">
                    <div class="si-stat-number"><?php echo esc_html(count($available_designs)); ?></div>
                    <div class="si-stat-label"><?php echo esc_html__('Designs', 'simple-invoice'); ?></div>
                    <div class="si-stat-meta"><?php echo esc_html__('Available designs', 'simple-invoice'); ?></div>
                </div>
            </div>

            <!-- Most Popular Design -->
            <div class="si-stat-card si-stat-popular">
                <div class="si-stat-icon">
                    <span class="dashicons dashicons-star-filled"></span>
                </div>
                <div class="si-stat-content">
                    <div class="si-stat-number"><?php
                        // Find most used design
                        $design_usage = array();
                        foreach ($templates as $template) {
                            $design_name = isset($available_designs[$template->design]) ? $available_designs[$template->design]['name'] : 'Classic';
                            $design_usage[$design_name] = isset($design_usage[$design_name]) ? $design_usage[$design_name] + 1 : 1;
                        }
                        $most_used = !empty($design_usage) ? array_keys($design_usage, max($design_usage))[0] : 'Classic';
                        echo esc_html($most_used);
                    ?></div>
                    <div class="si-stat-label"><?php echo esc_html__('Popular Design', 'simple-invoice'); ?></div>
                    <div class="si-stat-meta"><?php echo esc_html__('Most used', 'simple-invoice'); ?></div>
                </div>
            </div>

            <!-- Latest Template -->
            <div class="si-stat-card si-stat-latest">
                <div class="si-stat-icon">
                    <span class="dashicons dashicons-clock"></span>
                </div>
                <div class="si-stat-content">
                    <div class="si-stat-number"><?php
                        if (!empty($templates)) {
                            $latest_template = $templates[0];
                            foreach ($templates as $template) {
                                if (strtotime($template->created_at) > strtotime($latest_template->created_at)) {
                                    $latest_template = $template;
                                }
                            }
                            echo esc_html(date('M j', strtotime($latest_template->created_at)));
                        } else {
                            echo esc_html__('None', 'simple-invoice');
                        }
                    ?></div>
                    <div class="si-stat-label"><?php echo esc_html__('Latest', 'simple-invoice'); ?></div>
                    <div class="si-stat-meta"><?php echo esc_html__('Last created', 'simple-invoice'); ?></div>
                </div>
            </div>
        </div>
    </div>

    <!-- Quick Link to Designs -->
    <div class="si-designs-link-section">
        <div class="si-info-card">
            <div class="si-info-icon">
                <span class="dashicons dashicons-admin-appearance"></span>
            </div>
            <div class="si-info-content">
                <h3><?php echo esc_html__('Need a Different Design?', 'simple-invoice'); ?></h3>
                <p><?php echo esc_html__('Templates use designs for their visual appearance. Manage your invoice designs separately.', 'simple-invoice'); ?></p>
                <a href="<?php echo esc_url(admin_url('admin.php?page=wimp-designs')); ?>" class="si-btn si-btn-secondary">
                    <span class="dashicons dashicons-admin-appearance"></span>
                    <?php echo esc_html__('Manage Designs', 'simple-invoice'); ?>
                </a>
            </div>
        </div>
    </div>

    <!-- Templates Content -->
    <div class="si-templates-content">
        <div class="si-section-header">
            <h2 class="si-section-title">
                <span class="dashicons dashicons-media-text"></span>
                <?php echo esc_html__('Your Templates', 'simple-invoice'); ?>
            </h2>
            <p class="si-section-description">
                <?php echo esc_html__('Manage your invoice templates. Templates combine designs with custom field configurations.', 'simple-invoice'); ?>
            </p>
        </div>

        <?php if (!empty($templates)): ?>
            <!-- Templates Grid -->
            <div class="si-templates-grid">
                <?php foreach ($templates as $template): ?>
                    <div class="si-template-card" data-template-id="<?php echo esc_attr($template->id); ?>">
                        <div class="si-template-header">
                            <div class="si-template-preview">
                                <?php
                                $design = isset($available_designs[$template->design]) ? $available_designs[$template->design] : null;
                                if ($design && !empty($design['preview_url'])):
                                ?>
                                    <img src="<?php echo esc_url($design['preview_url']); ?>" alt="<?php echo esc_attr($template->name); ?>" />
                                <?php else: ?>
                                    <div class="si-template-placeholder">
                                        <span class="dashicons dashicons-media-text"></span>
                                    </div>
                                <?php endif; ?>
                            </div>
                            <div class="si-template-badge">
                                <span class="si-badge si-badge-design"><?php echo esc_html($design ? $design['name'] : 'Classic'); ?></span>
                            </div>
                        </div>

                        <div class="si-template-body">
                            <h3 class="si-template-title"><?php echo esc_html($template->name); ?></h3>
                            <div class="si-template-meta">
                                <span class="si-meta-item">
                                    <span class="dashicons dashicons-calendar-alt"></span>
                                    <?php echo esc_html(date('M j, Y', strtotime($template->created_at))); ?>
                                </span>
                                <span class="si-meta-item">
                                    <span class="dashicons dashicons-admin-appearance"></span>
                                    <?php echo esc_html($design ? $design['name'] : 'Classic'); ?>
                                </span>
                            </div>
                        </div>

                        <div class="si-template-footer">
                            <div class="si-template-actions">
                                <button type="button" class="si-btn si-btn-primary si-btn-sm si-use-template" data-template-id="<?php echo esc_attr($template->id); ?>">
                                    <span class="dashicons dashicons-yes"></span>
                                    <?php echo esc_html__('Use', 'simple-invoice'); ?>
                                </button>
                                <button type="button" class="si-btn si-btn-secondary si-btn-sm si-edit-template" data-template-id="<?php echo esc_attr($template->id); ?>">
                                    <span class="dashicons dashicons-edit"></span>
                                    <?php echo esc_html__('Edit', 'simple-invoice'); ?>
                                </button>
                                <button type="button" class="si-btn si-btn-danger si-btn-sm si-delete-template" data-template-id="<?php echo esc_attr($template->id); ?>">
                                    <span class="dashicons dashicons-trash"></span>
                                </button>
                            </div>
                        </div>
                    </div>
                <?php endforeach; ?>
            </div>
        <?php else: ?>
            <!-- Empty State -->
            <div class="si-empty-state">
                <div class="si-empty-content">
                    <div class="si-empty-icon">
                        <span class="dashicons dashicons-admin-page"></span>
                    </div>
                    <h3 class="si-empty-title"><?php echo esc_html__('No Templates Yet', 'simple-invoice'); ?></h3>
                    <p class="si-empty-description">
                        <?php echo esc_html__('Create your first invoice template to get started. Templates help you maintain consistent branding and save time when creating invoices.', 'simple-invoice'); ?>
                    </p>
                    <div class="si-empty-actions">
                        <a href="#" class="si-btn si-btn-primary si-add-template-btn">
                            <span class="dashicons dashicons-plus-alt"></span>
                            <?php echo esc_html__('Create Your First Template', 'simple-invoice'); ?>
                        </a>
                    </div>
                </div>
            </div>
        <?php endif; ?>
    </div>
</div>

<!-- Simple Add/Edit Template Modal -->
<div id="si-template-modal" class="si-modal si-modal-large" style="display: none;">
    <div class="si-modal-content">
        <div class="si-modal-header">
            <h2 id="si-template-modal-title"><?php echo esc_html__('Add New Template', 'simple-invoice'); ?></h2>
            <button type="button" class="si-modal-close">&times;</button>
        </div>

        <div class="si-modal-body">
            <form id="si-template-form">
                <input type="hidden" id="si-template-id" name="template_id" value="" />

                <div class="si-template-form-sections">

                    <!-- Basic Information -->
                    <div class="si-form-section">
                        <h3><?php echo esc_html__('Basic Information', 'simple-invoice'); ?></h3>

                        <table class="form-table">
                            <tr>
                                <th scope="row">
                                    <label for="si-template-name"><?php echo esc_html__('Template Name', 'simple-invoice'); ?> <span class="required">*</span></label>
                                </th>
                                <td>
                                    <input type="text"
                                           id="si-template-name"
                                           name="name"
                                           class="regular-text"
                                           required
                                           placeholder="<?php echo esc_attr__('e.g., Standard Invoice, Service Invoice', 'simple-invoice'); ?>" />
                                    <p class="description"><?php echo esc_html__('Give your template a unique name.', 'simple-invoice'); ?></p>
                                </td>
                            </tr>

                            <tr>
                                <th scope="row">
                                    <label for="si-template-design"><?php echo esc_html__('Design Layout', 'simple-invoice'); ?></label>
                                </th>
                                <td>
                                    <div class="si-design-selector">
                                        <?php foreach ($available_designs as $design_id => $design): ?>
                                            <label class="si-design-option">
                                                <input type="radio"
                                                       name="design"
                                                       value="<?php echo esc_attr($design_id); ?>"
                                                       <?php checked($design_id, 'classic'); ?> />
                                                <div class="si-design-preview">
                                                    <?php if (!empty($design['preview_url'])): ?>
                                                        <img src="<?php echo esc_url($design['preview_url']); ?>" alt="<?php echo esc_attr($design['name']); ?>" />
                                                    <?php else: ?>
                                                        <div class="si-design-placeholder">
                                                            <span class="dashicons dashicons-media-text"></span>
                                                        </div>
                                                    <?php endif; ?>
                                                    <span class="si-design-name"><?php echo esc_html($design['name']); ?></span>
                                                </div>
                                            </label>
                                        <?php endforeach; ?>
                                    </div>
                                </td>
                            </tr>
                        </table>
                    </div>
                    
                    <!-- Header Fields -->
                    <div class="si-form-section">
                        <h3><?php echo esc_html__('Header Fields', 'simple-invoice'); ?></h3>
                        <p class="description"><?php echo esc_html__('Select which business information to display in the invoice header.', 'simple-invoice'); ?></p>

                        <div class="si-checkbox-grid">
                            <label>
                                <input type="checkbox" name="header_fields[business_name]" value="1" checked />
                                <?php echo esc_html__('Business Name', 'simple-invoice'); ?>
                            </label>
                            <label>
                                <input type="checkbox" name="header_fields[business_logo]" value="1" checked />
                                <?php echo esc_html__('Business Logo', 'simple-invoice'); ?>
                            </label>
                            <label>
                                <input type="checkbox" name="header_fields[business_address]" value="1" checked />
                                <?php echo esc_html__('Business Address', 'simple-invoice'); ?>
                            </label>
                            <label>
                                <input type="checkbox" name="header_fields[business_email]" value="1" checked />
                                <?php echo esc_html__('Business Email', 'simple-invoice'); ?>
                            </label>
                            <label>
                                <input type="checkbox" name="header_fields[business_phone]" value="1" checked />
                                <?php echo esc_html__('Business Phone', 'simple-invoice'); ?>
                            </label>
                            <label>
                                <input type="checkbox" name="header_fields[gstin]" value="1" checked />
                                <?php echo esc_html__('GSTIN / Tax ID', 'simple-invoice'); ?>
                            </label>
                        </div>
                    </div>

                    <!-- Body Fields (Invoice Table) -->
                    <div class="si-form-section">
                        <h3><?php echo esc_html__('Invoice Table Fields', 'simple-invoice'); ?></h3>
                        <p class="description"><?php echo esc_html__('Configure the columns for your invoice items table.', 'simple-invoice'); ?></p>

                        <div id="si-body-fields">
                            <div class="si-fields-empty" id="si-body-fields-empty" style="display: none;">
                                <span class="dashicons dashicons-table-row-before"></span>
                                <h4><?php echo esc_html__('No Invoice Table Fields', 'simple-invoice'); ?></h4>
                                <p><?php echo esc_html__('Add fields to create your invoice table structure.', 'simple-invoice'); ?></p>
                            </div>
                        </div>

                        <button type="button" class="button" id="si-add-body-field">
                            <span class="dashicons dashicons-plus-alt"></span>
                            <?php echo esc_html__('Add New Field', 'simple-invoice'); ?>
                        </button>
                    </div>

                    <!-- Summary Fields -->
                    <div class="si-form-section">
                        <h3><?php echo esc_html__('Summary Fields', 'simple-invoice'); ?></h3>
                        <p class="description"><?php echo esc_html__('Configure the summary section below the invoice table.', 'simple-invoice'); ?></p>

                        <div id="si-summary-fields">
                            <div class="si-fields-empty" id="si-summary-fields-empty" style="display: none;">
                                <span class="dashicons dashicons-calculator"></span>
                                <h4><?php echo esc_html__('No Summary Fields', 'simple-invoice'); ?></h4>
                                <p><?php echo esc_html__('Add fields to create your invoice summary section.', 'simple-invoice'); ?></p>
                            </div>
                        </div>

                        <button type="button" class="button" id="si-add-summary-field">
                            <span class="dashicons dashicons-plus-alt"></span>
                            <?php echo esc_html__('Add Summary Field', 'simple-invoice'); ?>
                        </button>
                    </div>

                </div>
            </form>
        </div>

        <div class="si-modal-footer">
            <button type="button" class="button button-secondary si-modal-close"><?php echo esc_html__('Cancel', 'simple-invoice'); ?></button>
            <button type="button" class="button button-primary" id="si-save-template"><?php echo esc_html__('Save Template', 'simple-invoice'); ?></button>
        </div>
    </div>
</div>

<style>
/* Templates Page Modern Design */
.si-templates-page {
    background: #ffffff;
    margin: 0 -20px;
    padding: 0;
    min-height: 100vh;
}

/* Page Header */
.si-page-header {
    background: linear-gradient(135deg, #f47a45 0%, #5f5f5f 100%);
    color: #ffffff;
    padding: 30px 20px;
    margin-bottom: 30px;
}

.si-header-content {
    max-width: 1200px;
    margin: 0 auto;
    display: flex;
    justify-content: space-between;
    align-items: center;
    flex-wrap: wrap;
    gap: 20px;
}

.si-header-title {
    flex: 1;
}

.si-page-title {
    margin: 0 0 8px 0;
    font-size: 28px;
    font-weight: 700;
    color: #ffffff;
    display: flex;
    align-items: center;
    gap: 12px;
}

.si-page-title .dashicons {
    font-size: 32px;
    width: 32px;
    height: 32px;
}

.si-page-subtitle {
    margin: 0;
    font-size: 16px;
    color: #ffffff;
    opacity: 0.9;
    font-weight: 400;
}

.si-header-actions {
    flex-shrink: 0;
}

/* Modern Buttons */
.si-btn {
    display: inline-flex;
    align-items: center;
    gap: 8px;
    padding: 12px 20px;
    border: none;
    border-radius: 6px;
    font-size: 14px;
    font-weight: 600;
    text-decoration: none;
    cursor: pointer;
    transition: all 0.3s ease;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.si-btn:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 8px rgba(0,0,0,0.15);
    text-decoration: none;
}

.si-btn:active {
    transform: translateY(0);
}

.si-btn-primary {
    background: linear-gradient(135deg, #f47a45 0%, #5f5f5f 100%);
    color: #ffffff;
}

.si-btn-primary:hover {
    background: linear-gradient(135deg, #5f5f5f 0%, #000000 100%);
    color: #ffffff;
}

.si-btn-secondary {
    background: linear-gradient(135deg, #e7e7e7 0%, #ffffff 100%);
    color: #000000;
}

.si-btn-secondary:hover {
    background: linear-gradient(135deg, #ffffff 0%, #e7e7e7 100%);
    color: #000000;
}

.si-btn-danger {
    background: linear-gradient(135deg, #f47a45 0%, #5f5f5f 100%);
    color: #ffffff;
}

.si-btn-danger:hover {
    background: linear-gradient(135deg, #5f5f5f 0%, #000000 100%);
    color: #ffffff;
}

.si-btn-sm {
    padding: 8px 12px;
    font-size: 12px;
}

.si-btn .dashicons {
    font-size: 16px;
    width: 16px;
    height: 16px;
}

.si-btn-sm .dashicons {
    font-size: 14px;
    width: 14px;
    height: 14px;
}

/* Quick Stats */
.si-quick-stats {
    max-width: 1200px;
    margin: 0 auto 30px auto;
    padding: 0 20px;
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 20px;
}

.si-stat-item {
    background: white;
    border-radius: 8px;
    padding: 20px;
    display: flex;
    align-items: center;
    gap: 15px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    transition: transform 0.3s ease;
}

.si-stat-item:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0,0,0,0.15);
}

.si-stat-icon {
    width: 50px;
    height: 50px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    flex-shrink: 0;
}

.si-stat-icon .dashicons {
    font-size: 24px;
    width: 24px;
    height: 24px;
    color: white;
}

.si-stat-blue {
    background: linear-gradient(135deg, #f47a45 0%, #5f5f5f 100%);
}

.si-stat-green {
    background: linear-gradient(135deg, #f47a45 0%, #5f5f5f 100%);
}

.si-stat-orange {
    background: linear-gradient(135deg, #f47a45 0%, #5f5f5f 100%);
}

.si-stat-purple {
    background: linear-gradient(135deg, #f47a45 0%, #5f5f5f 100%);
}

.si-stat-info {
    flex: 1;
}

.si-stat-number {
    display: block;
    font-size: 24px;
    font-weight: 700;
    color: #000000;
    line-height: 1;
    margin-bottom: 4px;
}

.si-stat-label {
    display: block;
    font-size: 14px;
    color: #666;
    font-weight: 500;
}

/* Templates Content */
.si-templates-content {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
}

/* Templates Grid */
.si-templates-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(320px, 1fr));
    gap: 24px;
    margin-bottom: 30px;
}

/* Template Card */
.si-template-card {
    background: white;
    border-radius: 12px;
    overflow: hidden;
    box-shadow: 0 4px 6px rgba(0,0,0,0.1);
    transition: all 0.3s ease;
    border: 1px solid #e1e5e9;
}

.si-template-card:hover {
    transform: translateY(-4px);
    box-shadow: 0 8px 25px rgba(0,0,0,0.15);
}

/* Template Header */
.si-template-header {
    position: relative;
    height: 180px;
    overflow: hidden;
}

.si-template-preview {
    width: 100%;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
}

.si-template-preview img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.si-template-placeholder {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    color: #8e9aaf;
}

.si-template-placeholder .dashicons {
    font-size: 48px;
    width: 48px;
    height: 48px;
    margin-bottom: 8px;
}

.si-template-badge {
    position: absolute;
    top: 12px;
    right: 12px;
}

.si-badge {
    display: inline-block;
    padding: 4px 8px;
    border-radius: 12px;
    font-size: 11px;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.si-badge-design {
    background: rgba(255,255,255,0.9);
    color: #333;
    backdrop-filter: blur(10px);
}

/* Template Body */
.si-template-body {
    padding: 20px;
}

.si-template-title {
    margin: 0 0 12px 0;
    font-size: 18px;
    font-weight: 600;
    color: #2d3748;
    line-height: 1.3;
}

.si-template-meta {
    display: flex;
    flex-direction: column;
    gap: 6px;
}

.si-meta-item {
    display: flex;
    align-items: center;
    gap: 6px;
    font-size: 13px;
    color: #718096;
}

.si-meta-item .dashicons {
    font-size: 14px;
    width: 14px;
    height: 14px;
}

/* Template Footer */
.si-template-footer {
    padding: 16px 20px 20px 20px;
    border-top: 1px solid #e2e8f0;
    background: #f8f9fa;
}

.si-template-actions {
    display: flex;
    gap: 8px;
    justify-content: space-between;
}

.si-template-actions .si-btn {
    flex: 1;
    justify-content: center;
    min-width: 0;
}

/* Empty State */
.si-empty-state {
    background: white;
    border-radius: 12px;
    padding: 60px 40px;
    text-align: center;
    box-shadow: 0 4px 6px rgba(0,0,0,0.1);
    border: 1px solid #e1e5e9;
}

.si-empty-content {
    max-width: 400px;
    margin: 0 auto;
}

.si-empty-icon {
    width: 80px;
    height: 80px;
    margin: 0 auto 24px auto;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
}

.si-empty-icon .dashicons {
    font-size: 40px;
    width: 40px;
    height: 40px;
    color: white;
}

.si-empty-title {
    margin: 0 0 12px 0;
    font-size: 24px;
    font-weight: 600;
    color: #2d3748;
}

.si-empty-description {
    margin: 0 0 24px 0;
    font-size: 16px;
    color: #718096;
    line-height: 1.5;
}

.si-empty-actions {
    margin-top: 24px;
}

/* Modal Form Sections */
.si-templates-page .si-form-section {
    background: #ffffff;
    border: 1px solid #ddd;
    border-radius: 6px;
    padding: 20px;
    margin-bottom: 20px;
    box-shadow: 0 1px 3px rgba(0,0,0,0.1);
}

.si-templates-page .si-form-section h3 {
    margin: 0 0 12px 0;
    padding: 0 0 8px 0;
    border-bottom: 1px solid #ddd;
    color: #23282d;
    font-size: 16px;
    font-weight: 600;
}

.si-templates-page .si-form-section .description {
    color: #666;
    margin-bottom: 15px;
    font-size: 13px;
    line-height: 1.4;
}

/* Responsive Design */
@media (max-width: 768px) {
    .si-header-content {
        flex-direction: column;
        text-align: center;
        gap: 15px;
    }

    .si-page-title {
        font-size: 24px;
        justify-content: center;
    }

    .si-quick-stats {
        grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
        gap: 15px;
    }

    .si-stat-item {
        padding: 15px;
        flex-direction: column;
        text-align: center;
        gap: 10px;
    }

    .si-stat-icon {
        width: 40px;
        height: 40px;
    }

    .si-stat-icon .dashicons {
        font-size: 20px;
        width: 20px;
        height: 20px;
    }

    .si-stat-number {
        font-size: 20px;
    }

    .si-templates-grid {
        grid-template-columns: 1fr;
        gap: 20px;
    }

    .si-template-actions {
        flex-direction: column;
    }

    .si-template-actions .si-btn {
        flex: none;
    }

    .si-empty-state {
        padding: 40px 20px;
    }

    .si-empty-title {
        font-size: 20px;
    }

    .si-empty-description {
        font-size: 14px;
    }
}

@media (max-width: 480px) {
    .si-page-header {
        padding: 20px 15px;
    }

    .si-templates-content {
        padding: 0 15px;
    }

    .si-quick-stats {
        padding: 0 15px;
        grid-template-columns: 1fr;
    }
}

/* Field Row Clean Styling */
.si-field-row {
    background: #ffffff;
    border: 1px solid #ddd;
    border-radius: 4px;
    padding: 15px;
    margin-bottom: 15px;
    position: relative;
    transition: border-color 0.2s ease;
}

.si-field-row:hover {
    border-color: #0073aa;
}

/* Field Inputs Simple Layout */
.si-field-inputs {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 12px;
    margin-bottom: 15px;
    padding: 12px;
    background: #f9f9f9;
    border-radius: 3px;
    border: 1px solid #e5e5e5;
}

.si-field-inputs .si-input-group {
    display: flex;
    flex-direction: column;
}

.si-field-inputs label {
    font-weight: 600;
    color: #23282d;
    margin-bottom: 4px;
    font-size: 12px;
}

.si-field-inputs input[type="text"] {
    padding: 6px 8px;
    border: 1px solid #ddd;
    border-radius: 3px;
    font-size: 13px;
    background: #fff;
}

.si-field-inputs input[type="text"]:focus {
    border-color: #0073aa;
    outline: none;
    box-shadow: 0 0 0 1px #0073aa;
}

.si-field-inputs .description {
    color: #666;
    font-size: 11px;
    margin-top: 6px;
    grid-column: 1 / -1;
    line-height: 1.3;
}

/* Field Controls Simple */
.si-field-controls {
    display: flex;
    align-items: center;
    gap: 12px;
    flex-wrap: wrap;
    margin-bottom: 12px;
}

.si-field-controls select {
    padding: 6px 8px;
    border: 1px solid #ddd;
    border-radius: 3px;
    font-size: 13px;
    min-width: 120px;
    background: #fff;
}

.si-field-controls label {
    display: flex;
    align-items: center;
    gap: 4px;
    font-size: 13px;
    color: #23282d;
}

.si-field-controls input[type="checkbox"] {
    margin: 0;
}

/* Formula Field Simple */
.si-formula-field {
    margin-top: 12px;
    padding: 12px;
    background: #f0f8ff;
    border: 1px solid #cce7ff;
    border-radius: 3px;
    border-left: 3px solid #0073aa;
}

.si-formula-field label {
    display: block;
    font-weight: 600;
    color: #0073aa;
    margin-bottom: 6px;
    font-size: 12px;
}

.si-formula-field input[type="text"] {
    width: 100%;
    padding: 6px 8px;
    border: 1px solid #b3d9ff;
    border-radius: 3px;
    font-size: 13px;
    font-family: 'Courier New', monospace;
    background: #fff;
    margin-bottom: 8px;
}

.si-formula-field input[type="text"]:focus {
    border-color: #0073aa;
    outline: none;
    box-shadow: 0 0 0 1px #0073aa;
}

/* Available Fields Simple */
.si-available-fields {
    background: #fffbf0;
    border: 1px solid #f0e68c;
    border-radius: 3px;
    padding: 8px;
    margin-top: 6px;
}

.si-available-fields .description {
    margin: 0;
    color: #8b4513;
    font-size: 11px;
}

.si-field-names-list {
    font-weight: 600;
    color: #0073aa;
    font-family: 'Courier New', monospace;
    background: #fff;
    padding: 1px 4px;
    border-radius: 2px;
    border: 1px solid #ddd;
}

/* Remove Button Simple */
.si-remove-field {
    position: absolute;
    top: 10px;
    right: 10px;
    background: #dc3545 !important;
    border: 1px solid #dc3545 !important;
    color: white !important;
    padding: 4px 8px;
    border-radius: 3px;
    font-size: 11px;
    font-weight: 600;
    cursor: pointer;
    line-height: 1;
    z-index: 10;
    pointer-events: auto;
    text-decoration: none;
}

.si-remove-field:hover {
    background: #c82333 !important;
    border-color: #bd2130 !important;
    color: white !important;
}

.si-remove-field:focus {
    outline: none;
    box-shadow: 0 0 0 2px rgba(220, 53, 69, 0.3);
}

/* Add Field Buttons Simple */
.si-form-section .button {
    background: #0073aa;
    border-color: #0073aa;
    color: white;
    padding: 8px 16px;
    border-radius: 3px;
    font-weight: 600;
    font-size: 13px;
    display: inline-flex;
    align-items: center;
    gap: 6px;
    text-decoration: none;
}

.si-form-section .button:hover {
    background: #005a87;
    border-color: #005a87;
    color: white;
}

.si-form-section .button .dashicons {
    font-size: 14px;
    width: 14px;
    height: 14px;
}

/* Modal Footer Simple */
.si-modal-footer {
    padding: 15px 20px;
    background: #f9f9f9;
    border-top: 1px solid #ddd;
    display: flex;
    justify-content: flex-end;
    gap: 10px;
}

.si-modal-footer .button {
    padding: 8px 16px;
    border-radius: 3px;
    font-weight: 600;
    font-size: 13px;
    min-width: 100px;
    text-decoration: none;
}

.si-modal-footer .button-primary {
    background: #0073aa;
    border-color: #0073aa;
    color: white;
}

.si-modal-footer .button-primary:hover {
    background: #005a87;
    border-color: #005a87;
    color: white;
}

.si-modal-footer .button-secondary {
    background: #f1f1f1;
    border-color: #ddd;
    color: #666;
}

.si-modal-footer .button-secondary:hover {
    background: #e1e1e1;
    border-color: #ccc;
    color: #333;
}

/* Modal Header Simple */
.si-modal-header {
    padding: 15px 20px;
    background: #0073aa;
    color: white;
    border-bottom: 1px solid #ddd;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.si-modal-header h2 {
    margin: 0;
    font-size: 18px;
    font-weight: 600;
    color: white;
}

.si-modal-close {
    background: none;
    border: none;
    color: white;
    font-size: 20px;
    cursor: pointer;
    padding: 4px;
    border-radius: 2px;
    line-height: 1;
}

.si-modal-close:hover {
    background: rgba(255,255,255,0.2);
}

/* Empty State Simple */
.si-fields-empty {
    text-align: center;
    padding: 30px 20px;
    color: #666;
    background: #f9f9f9;
    border: 1px dashed #ccc;
    border-radius: 4px;
    margin-bottom: 15px;
}

.si-fields-empty .dashicons {
    font-size: 36px;
    color: #ccc;
    margin-bottom: 10px;
}

.si-fields-empty h4 {
    margin: 0 0 8px 0;
    color: #333;
    font-size: 14px;
}

.si-fields-empty p {
    margin: 0;
    font-size: 12px;
}

/* Field Type Icons */
.si-field-type-select option {
    padding: 8px;
}

/* Formula Validation Simple */
.si-formula-error {
    color: #dc3545;
    font-size: 11px;
    margin-top: 4px;
    padding: 4px 6px;
    background: #f8d7da;
    border: 1px solid #f5c6cb;
    border-radius: 2px;
}

.si-formula-success {
    color: #155724;
    font-size: 11px;
    margin-top: 4px;
    padding: 4px 6px;
    background: #d4edda;
    border: 1px solid #c3e6cb;
    border-radius: 2px;
}

/* Field Name Validation */
.si-field-name-error {
    color: #dc3545;
    font-size: 10px;
    margin-top: 2px;
    padding: 2px 4px;
    background: #f8d7da;
    border: 1px solid #f5c6cb;
    border-radius: 2px;
    position: absolute;
    z-index: 100;
    white-space: nowrap;
}

.si-field-name {
    font-family: 'Courier New', monospace !important;
    font-size: 13px !important;
}

.si-field-name:invalid {
    border-color: #dc3545 !important;
    box-shadow: 0 0 0 1px #dc3545 !important;
}

/* Responsive Design */
@media (max-width: 768px) {
    .si-field-inputs {
        grid-template-columns: 1fr;
        gap: 10px;
    }

    .si-field-controls {
        flex-direction: column;
        align-items: stretch;
        gap: 10px;
    }

    .si-field-controls select {
        min-width: auto;
    }

    .si-remove-field {
        position: static;
        margin-top: 10px;
        align-self: flex-start;
    }

    .si-modal-footer {
        flex-direction: column;
    }

    .si-modal-footer .button {
        width: 100%;
        justify-content: center;
    }
}
</style>

<script>
jQuery(document).ready(function($) {

    var templateModal = $('#si-template-modal');
    var templateForm = $('#si-template-form');
    var isEditing = false;
    
    // Default body fields
    var defaultBodyFields = [
        { label: 'Sr No.', name: 'sr_no', type: 'serial', required: true, editable: false },
        { label: 'Product/Service', name: 'product', type: 'text', required: true, editable: true },
        { label: 'Quantity', name: 'quantity', type: 'number', required: true, editable: true },
        { label: 'Rate', name: 'rate', type: 'currency', required: true, editable: true },
        { label: 'Total', name: 'total', type: 'calculated', formula: 'quantity * rate', required: true, editable: false }
    ];

    // Default summary fields
    var defaultSummaryFields = [
        { label: 'Subtotal', name: 'subtotal', type: 'calculated', formula: 'sum_total', required: true },
        { label: 'Tax (%)', name: 'tax', type: 'percentage', required: false },
        { label: 'Discount', name: 'discount', type: 'currency', required: false },
        { label: 'Shipping', name: 'shipping', type: 'currency', required: false },
        { label: 'Total Amount', name: 'total_amount', type: 'calculated', formula: 'subtotal + tax - discount + shipping', required: true }
    ];
    
    // Open add template modal
    $('.si-add-template-btn').on('click', function(e) {
        e.preventDefault();
        openTemplateModal();
    });

    // Open edit template modal
    $(document).on('click', '.si-edit-template', function() {
        var templateId = $(this).data('template-id');
        openTemplateModal(templateId);
    });

    // Use template for creating invoice
    $(document).on('click', '.si-use-template', function() {
        var templateId = $(this).data('template-id');
        window.location.href = '<?php echo admin_url('admin.php?page=wimp-create-invoice'); ?>&template_id=' + templateId;
    });

    // Delete template
    $(document).on('click', '.si-delete-template', function() {
        var templateId = $(this).data('template-id');
        var templateCard = $(this).closest('.si-template-card');
        var templateName = templateCard.find('.si-template-title').text();

        if (confirm('<?php echo esc_js(__('Are you sure you want to delete the template', 'simple-invoice')); ?> "' + templateName + '"? <?php echo esc_js(__('This action cannot be undone.', 'simple-invoice')); ?>')) {
            deleteTemplate(templateId, templateCard);
        }
    });

    // Delete template
    $(document).on('click', '.si-delete-template', function() {
        var templateId = $(this).data('template-id');
        var templateName = $(this).closest('.si-template-card').find('h3').text();

        if (confirm('<?php echo esc_js(__('Are you sure you want to delete', 'simple-invoice')); ?> "' + templateName + '"?')) {
            deleteTemplate(templateId);
        }
    });

    // Close modal
    $('.si-modal-close').on('click', function() {
        closeTemplateModal();
    });

    // Close modal when clicking backdrop
    $(document).on('click', '.si-modal-large', function(e) {
        if (e.target === this) {
            closeTemplateModal();
        }
    });

    // Prevent modal close when clicking inside content
    $('.si-modal-content').on('click', function(e) {
        e.stopPropagation();
    });

    // Save template
    $('#si-save-template').on('click', function() {
        saveTemplate();
    });
    
    function openTemplateModal(templateId) {
        isEditing = !!templateId;

        if (isEditing) {
            $('#si-template-modal-title').text('<?php echo esc_js(__('Edit Template', 'simple-invoice')); ?>');
            $('#si-save-template').text('<?php echo esc_js(__('Update Template', 'simple-invoice')); ?>');
            loadTemplateData(templateId);
        } else {
            $('#si-template-modal-title').text('<?php echo esc_js(__('Add New Template', 'simple-invoice')); ?>');
            $('#si-save-template').text('<?php echo esc_js(__('Save Template', 'simple-invoice')); ?>');
            templateForm[0].reset();
            $('#si-template-id').val('');
            loadDefaultFields();
        }

        templateModal.show();
    }

    function closeTemplateModal() {
        templateModal.hide();
        templateForm[0].reset();
        isEditing = false;
    }

    function loadTemplateData(templateId) {

        $.ajax({
            url: ajaxurl,
            type: 'POST',
            data: {
                action: 'si_get_template',
                template_id: templateId,
                nonce: '<?php echo wp_create_nonce('si_get_template_nonce'); ?>'
            },
            success: function(response) {
                if (response.success && response.data) {
                    var template = response.data;

                    // Fill basic information
                    $('#si-template-name').val(template.name || '');
                    $('#si-template-id').val(template.id || '');

                    // Set design selection
                    if (template.design) {
                        $('input[name="design"][value="' + template.design + '"]').prop('checked', true);
                    }

                    // Set header fields
                    if (template.header_fields) {
                        var headerFields = typeof template.header_fields === 'string' ?
                            JSON.parse(template.header_fields) : template.header_fields;

                        $.each(headerFields, function(field, value) {
                            $('input[name="header_fields[' + field + ']"]').prop('checked', !!value);
                        });
                    }

                    // Clear existing fields
                    $('#si-body-fields .si-field-row').remove();
                    $('#si-summary-fields .si-field-row').remove();

                    // Load body fields
                    if (template.body_fields) {
                        var bodyFields = typeof template.body_fields === 'string' ?
                            JSON.parse(template.body_fields) : template.body_fields;

                        if (Array.isArray(bodyFields)) {
                            bodyFields.forEach(function(field, index) {
                                addBodyField(field, index);
                            });
                        }
                    }

                    // Load summary fields
                    if (template.summary_fields) {
                        var summaryFields = typeof template.summary_fields === 'string' ?
                            JSON.parse(template.summary_fields) : template.summary_fields;

                        if (Array.isArray(summaryFields)) {
                            summaryFields.forEach(function(field, index) {
                                addSummaryField(field, index);
                            });
                        }
                    }

                    // Update available fields list
                    updateAvailableFieldsList();
                    toggleEmptyState();

                } else {
                    alert('<?php echo esc_js(__('Failed to load template data. Please try again.', 'simple-invoice')); ?>');
                }
            },
            error: function(xhr, status, error) {
                alert('<?php echo esc_js(__('Error loading template data. Please try again.', 'simple-invoice')); ?>');
            }
        });
    }

    function deleteTemplate(templateId, templateCard) {

        // Show loading state
        templateCard.css('opacity', '0.5');

        $.ajax({
            url: ajaxurl,
            type: 'POST',
            data: {
                action: 'si_delete_template',
                template_id: templateId,
                nonce: '<?php echo wp_create_nonce('si_delete_template_nonce'); ?>'
            },
            success: function(response) {
                if (response.success) {
                    // Remove template card with animation
                    templateCard.fadeOut(300, function() {
                        $(this).remove();

                        // Check if no templates left
                        if ($('.si-template-card').length === 0) {
                            location.reload(); // Reload to show empty state
                        }
                    });

                    // Show success message (you can implement a toast notification here)
                    alert('<?php echo esc_js(__('Template deleted successfully.', 'simple-invoice')); ?>');

                } else {
                    templateCard.css('opacity', '1');
                    alert(response.data || '<?php echo esc_js(__('Failed to delete template. Please try again.', 'simple-invoice')); ?>');
                }
            },
            error: function(xhr, status, error) {
                templateCard.css('opacity', '1');
                alert('<?php echo esc_js(__('Error deleting template. Please try again.', 'simple-invoice')); ?>');
            }
        });
    }
    
    function loadDefaultFields() {
        // Load default body fields
        var bodyFieldsContainer = $('#si-body-fields');
        bodyFieldsContainer.empty();
        
        defaultBodyFields.forEach(function(field, index) {
            addBodyField(field, index);
        });
        
        // Load default summary fields
        var summaryFieldsContainer = $('#si-summary-fields');
        summaryFieldsContainer.empty();
        
        defaultSummaryFields.forEach(function(field, index) {
            addSummaryField(field, index);
        });

        toggleEmptyState();

        // Ensure all formula fields are properly shown/hidden and remove buttons work
        setTimeout(function() {
            $('.si-field-type-select').each(function() {
                var fieldRow = $(this).closest('.si-field-row');
                var formulaField = fieldRow.find('.si-formula-field');

                if ($(this).val() === 'calculated') {
                    formulaField.show();
                } else {
                    formulaField.hide();
                }
            });

            // Ensure all remove buttons work
            $('.si-remove-field').each(function() {
                var button = $(this);
                button.off('click').on('click', function(e) {
                    e.preventDefault();
                    e.stopPropagation();
                    $(this).closest('.si-field-row').remove();
                    updateAvailableFieldsList();
                    toggleEmptyState();
                });
            });
        }, 100);
    }
    
    function addBodyField(field, index) {
        var fieldHtml = '<div class="si-field-row" data-index="' + index + '">' +
            '<button type="button" class="si-remove-field">✕</button>' +
            '<div class="si-field-inputs">' +
                '<div class="si-input-group">' +
                    '<label>Field Label</label>' +
                    '<input type="text" name="body_fields[' + index + '][label]" value="' + (field.label || '') + '" placeholder="e.g., ABCD, Product Name" class="si-field-label" />' +
                '</div>' +
                '<div class="si-input-group">' +
                    '<label>Field Name</label>' +
                    '<input type="text" name="body_fields[' + index + '][name]" value="' + (field.name || '') + '" placeholder="e.g., abcd, product_name" class="si-field-name" pattern="[a-z_]+" title="Only lowercase letters and underscore" />' +
                '</div>' +
                '<small class="description">Field Name for formulas (lowercase + underscore only)</small>' +
            '</div>' +
            '<div class="si-field-controls">' +
                '<select name="body_fields[' + index + '][type]" class="si-field-type-select">' +
                    '<option value="text"' + (field.type === 'text' ? ' selected' : '') + '>Text</option>' +
                    '<option value="number"' + (field.type === 'number' ? ' selected' : '') + '>Number</option>' +
                    '<option value="currency"' + (field.type === 'currency' ? ' selected' : '') + '>Currency</option>' +
                    '<option value="calculated"' + (field.type === 'calculated' ? ' selected' : '') + '>Calculated</option>' +
                    '<option value="serial"' + (field.type === 'serial' ? ' selected' : '') + '>Serial Number</option>' +
                '</select>' +
                '<label><input type="checkbox" name="body_fields[' + index + '][required]" value="1"' + (field.required ? ' checked' : '') + ' /> Required</label>' +
            '</div>' +
            '<div class="si-formula-field" style="' + (field.type === 'calculated' ? 'display:block;' : 'display:none;') + '">' +
                '<label>Formula</label>' +
                '<input type="text" name="body_fields[' + index + '][formula]" value="' + (field.formula || '') + '" placeholder="e.g., quantity * rate, abcd + subtotal" />' +
                '<div class="si-available-fields">' +
                    '<small class="description"><strong>Available:</strong> <span class="si-field-names-list">quantity, rate, subtotal</span></small>' +
                '</div>' +
            '</div>' +
        '</div>';

        $('#si-body-fields').append(fieldHtml);
        updateAvailableFieldsList();
        toggleEmptyState();

        // Ensure formula field visibility is correct
        var addedRow = $('#si-body-fields .si-field-row').last();
        var typeSelect = addedRow.find('.si-field-type-select');
        var formulaField = addedRow.find('.si-formula-field');

        if (typeSelect.val() === 'calculated') {
            formulaField.show();
        } else {
            formulaField.hide();
        }

        // Ensure remove button is clickable
        var removeButton = addedRow.find('.si-remove-field');
        removeButton.off('click').on('click', function(e) {
            e.preventDefault();
            e.stopPropagation();
            $(this).closest('.si-field-row').remove();
            updateAvailableFieldsList();
            toggleEmptyState();
        });
    }
    
    function addSummaryField(field, index) {
        var fieldHtml = '<div class="si-field-row" data-index="' + index + '">' +
            '<button type="button" class="si-remove-field">✕</button>' +
            '<div class="si-field-inputs">' +
                '<div class="si-input-group">' +
                    '<label>Field Label</label>' +
                    '<input type="text" name="summary_fields[' + index + '][label]" value="' + (field.label || '') + '" placeholder="e.g., ABCD Total, Final Amount" class="si-field-label" />' +
                '</div>' +
                '<div class="si-input-group">' +
                    '<label>Field Name</label>' +
                    '<input type="text" name="summary_fields[' + index + '][name]" value="' + (field.name || '') + '" placeholder="e.g., abcd_total, final_amount" class="si-field-name" pattern="[a-z_]+" title="Only lowercase letters and underscore" />' +
                '</div>' +
                '<small class="description">Field Name for formulas (lowercase + underscore only)</small>' +
            '</div>' +
            '<div class="si-field-controls">' +
                '<select name="summary_fields[' + index + '][type]" class="si-field-type-select">' +
                    '<option value="currency"' + (field.type === 'currency' ? ' selected' : '') + '>Currency</option>' +
                    '<option value="percentage"' + (field.type === 'percentage' ? ' selected' : '') + '>Percentage</option>' +
                    '<option value="calculated"' + (field.type === 'calculated' ? ' selected' : '') + '>Calculated</option>' +
                '</select>' +
                '<label><input type="checkbox" name="summary_fields[' + index + '][required]" value="1"' + (field.required ? ' checked' : '') + ' /> Required</label>' +
            '</div>' +
            '<div class="si-formula-field" style="' + (field.type === 'calculated' ? 'display:block;' : 'display:none;') + '">' +
                '<label>Formula</label>' +
                '<input type="text" name="summary_fields[' + index + '][formula]" value="' + (field.formula || '') + '" placeholder="e.g., subtotal + tax - discount, abcd_total + subtotal" />' +
                '<div class="si-available-fields">' +
                    '<small class="description"><strong>Available:</strong> <span class="si-field-names-list">subtotal, tax, discount, shipping</span></small>' +
                '</div>' +
            '</div>' +
        '</div>';

        $('#si-summary-fields').append(fieldHtml);
        updateAvailableFieldsList();
        toggleEmptyState();

        // Ensure formula field visibility is correct
        var addedRow = $('#si-summary-fields .si-field-row').last();
        var typeSelect = addedRow.find('.si-field-type-select');
        var formulaField = addedRow.find('.si-formula-field');

        if (typeSelect.val() === 'calculated') {
            formulaField.show();
        } else {
            formulaField.hide();
        }

        // Ensure remove button is clickable
        var removeButton = addedRow.find('.si-remove-field');
        removeButton.off('click').on('click', function(e) {
            e.preventDefault();
            e.stopPropagation();
            $(this).closest('.si-field-row').remove();
            updateAvailableFieldsList();
            toggleEmptyState();
        });
    }
    
    // Add new body field
    $('#si-add-body-field').on('click', function() {
        var index = $('#si-body-fields .si-field-row').length;
        addBodyField({}, index);
    });
    
    // Add new summary field
    $('#si-add-summary-field').on('click', function() {
        var index = $('#si-summary-fields .si-field-row').length;
        addSummaryField({}, index);
    });
    
    // Remove field
    $(document).on('click', '.si-remove-field', function(e) {
        e.preventDefault();
        e.stopPropagation();

        var fieldRow = $(this).closest('.si-field-row');

        if (fieldRow.length > 0) {
            fieldRow.remove();

            updateAvailableFieldsList();
            toggleEmptyState();
        }
    });

    // Show/hide formula field based on field type
    $(document).on('change', '.si-field-type-select', function() {
        var fieldRow = $(this).closest('.si-field-row');
        var formulaField = fieldRow.find('.si-formula-field');
        var selectedValue = $(this).val();

        if (selectedValue === 'calculated') {
            formulaField.show();
        } else {
            formulaField.hide();
        }
    });

    // Update available fields list when field name changes
    $(document).on('input', '.si-field-name', function() {
        updateAvailableFieldsList();
    });

    // Additional remove button handler (backup)
    $(document).on('mousedown', '.si-remove-field', function(e) {
        e.preventDefault();
    });

    // Touch support for mobile
    $(document).on('touchstart', '.si-remove-field', function(e) {
        e.preventDefault();
        $(this).trigger('click');
    });

    // Validate formula on input
    $(document).on('input', '.si-formula-field input[type="text"]', function() {
        validateFormula($(this));
    });

    // Auto-generate field name from label
    $(document).on('input', '.si-field-label', function() {
        var nameField = $(this).closest('.si-field-inputs').find('.si-field-name');
        if (!nameField.val()) {
            var generatedName = $(this).val().toLowerCase()
                .replace(/[^a-z\s]/g, '')     // Only letters and spaces
                .replace(/\s+/g, '_')         // Replace spaces with underscore
                .replace(/^_+/, '')           // Remove leading underscores
                .replace(/_+/g, '_')          // Replace multiple underscores with single
                .substring(0, 25);            // Limit length
            nameField.val(generatedName);
            updateAvailableFieldsList();
        }
    });

    // Validate field name input - only lowercase letters and underscore
    $(document).on('input', '.si-field-name', function() {
        var input = $(this);
        var value = input.val();
        var cleanValue = value.toLowerCase()
            .replace(/[^a-z_]/g, '')  // Only allow lowercase letters and underscore
            .replace(/^_+/, '')       // Remove leading underscores
            .replace(/_+/g, '_')      // Replace multiple underscores with single
            .substring(0, 30);        // Limit length

        if (value !== cleanValue) {
            input.val(cleanValue);

            // Show validation message
            var existingMsg = input.siblings('.si-field-name-error');
            existingMsg.remove();

            if (value !== cleanValue) {
                input.after('<div class="si-field-name-error">Only lowercase letters and underscore allowed</div>');
                setTimeout(function() {
                    input.siblings('.si-field-name-error').fadeOut(function() {
                        $(this).remove();
                    });
                }, 2000);
            }
        }

        updateAvailableFieldsList();
    });

    // Prevent invalid characters on keypress
    $(document).on('keypress', '.si-field-name', function(e) {
        var char = String.fromCharCode(e.which);
        var allowedChars = /[a-z_]/;

        if (!allowedChars.test(char) && e.which !== 8 && e.which !== 0) {
            e.preventDefault();
            return false;
        }
    });

    // Prevent paste of invalid content
    $(document).on('paste', '.si-field-name', function(e) {
        var input = $(this);
        setTimeout(function() {
            var value = input.val();
            var cleanValue = value.toLowerCase()
                .replace(/[^a-z_]/g, '')
                .replace(/^_+/, '')
                .replace(/_+/g, '_')
                .substring(0, 30);
            input.val(cleanValue);
            updateAvailableFieldsList();
        }, 1);
    });

    // Function to update available fields list in formula descriptions
    function updateAvailableFieldsList() {
        var bodyFieldNames = [];
        var summaryFieldNames = [];

        // Collect body field names
        $('#si-body-fields .si-field-name').each(function() {
            var name = $(this).val().trim();
            if (name) {
                bodyFieldNames.push(name);
            }
        });

        // Collect summary field names
        $('#si-summary-fields .si-field-name').each(function() {
            var name = $(this).val().trim();
            if (name) {
                summaryFieldNames.push(name);
            }
        });

        // Add default field names
        var defaultBodyFields = ['quantity', 'rate', 'subtotal'];
        var defaultSummaryFields = ['subtotal', 'tax', 'discount', 'shipping'];

        var allBodyFields = defaultBodyFields.concat(bodyFieldNames);
        var allSummaryFields = defaultSummaryFields.concat(summaryFieldNames).concat(bodyFieldNames);

        // Update body fields available list
        $('#si-body-fields .si-field-names-list').text(allBodyFields.join(', '));

        // Update summary fields available list
        $('#si-summary-fields .si-field-names-list').text(allSummaryFields.join(', '));
    }

    // Formula validation function
    function validateFormula(formulaInput) {
        var formula = formulaInput.val().trim();
        var errorContainer = formulaInput.siblings('.si-formula-error');
        var successContainer = formulaInput.siblings('.si-formula-success');

        // Remove existing validation messages
        errorContainer.remove();
        successContainer.remove();

        if (!formula) {
            return;
        }

        // Get available field names
        var availableFields = [];
        formulaInput.closest('.si-field-row').parent().find('.si-field-name').each(function() {
            var name = $(this).val().trim();
            if (name) {
                availableFields.push(name);
            }
        });

        // Add default fields
        if (formulaInput.closest('#si-body-fields').length) {
            availableFields = availableFields.concat(['quantity', 'rate', 'subtotal']);
        } else {
            availableFields = availableFields.concat(['subtotal', 'tax', 'discount', 'shipping']);
        }

        // Basic formula validation
        var isValid = true;
        var errorMessage = '';

        // Check for basic syntax
        if (!/^[a-zA-Z0-9_\s\+\-\*\/\(\)\.]+$/.test(formula)) {
            isValid = false;
            errorMessage = 'Formula contains invalid characters. Use only letters, numbers, +, -, *, /, (, )';
        }

        // Check for field names
        var fieldNames = formula.match(/[a-zA-Z_][a-zA-Z0-9_]*/g) || [];
        for (var i = 0; i < fieldNames.length; i++) {
            if (availableFields.indexOf(fieldNames[i]) === -1 &&
                !['sum_total'].includes(fieldNames[i])) {
                isValid = false;
                errorMessage = 'Unknown field: "' + fieldNames[i] + '". Available fields: ' + availableFields.join(', ');
                break;
            }
        }

        // Show validation result
        if (isValid) {
            formulaInput.after('<div class="si-formula-success">✓ Formula looks good!</div>');
        } else {
            formulaInput.after('<div class="si-formula-error">✗ ' + errorMessage + '</div>');
        }
    }

    // Toggle empty state visibility
    function toggleEmptyState() {
        // Body fields empty state
        var bodyFieldsCount = $('#si-body-fields .si-field-row').length;
        if (bodyFieldsCount === 0) {
            $('#si-body-fields-empty').show();
        } else {
            $('#si-body-fields-empty').hide();
        }

        // Summary fields empty state
        var summaryFieldsCount = $('#si-summary-fields .si-field-row').length;
        if (summaryFieldsCount === 0) {
            $('#si-summary-fields-empty').show();
        } else {
            $('#si-summary-fields-empty').hide();
        }
    }






    
    function saveTemplate() {
        var formData = templateForm.serialize();
        var action = isEditing ? 'si_edit_template' : 'si_add_template';
        var nonce = isEditing ? '<?php echo wp_create_nonce('si_edit_template_nonce'); ?>' : '<?php echo wp_create_nonce('si_add_template_nonce'); ?>';
        
        formData += '&action=' + action + '&nonce=' + nonce;
        
        $.ajax({
            url: ajaxurl,
            type: 'POST',
            data: formData,
            success: function(response) {
                if (response.success) {
                    closeTemplateModal();
                    location.reload(); // Refresh the page to show updated data
                } else {
                    alert(response.message || '<?php echo esc_js(__('An error occurred.', 'simple-invoice')); ?>');
                }
            },
            error: function() {
                alert('<?php echo esc_js(__('An error occurred. Please try again.', 'simple-invoice')); ?>');
            }
        });
    }
    
    function deleteTemplate(templateId) {
        $.ajax({
            url: ajaxurl,
            type: 'POST',
            data: {
                action: 'si_delete_template',
                template_id: templateId,
                nonce: '<?php echo wp_create_nonce('si_delete_template_nonce'); ?>'
            },
            success: function(response) {
                if (response.success) {
                    $('.si-template-card[data-template-id="' + templateId + '"]').fadeOut(function() {
                        $(this).remove();
                        
                        // Check if grid is empty
                        if ($('.si-template-card').length === 0) {
                            location.reload();
                        }
                    });
                } else {
                    alert(response.message || '<?php echo esc_js(__('Failed to delete template.', 'simple-invoice')); ?>');
                }
            },
            error: function() {
                alert('<?php echo esc_js(__('An error occurred. Please try again.', 'simple-invoice')); ?>');
            }
        });
    }
    
    // Initialize default fields on page load
    loadDefaultFields();

    // Design Guide Modal
    $('.si-view-guide').on('click', function(e) {
        e.preventDefault();
        $('#si-design-guide-modal').fadeIn(300);
    });

    // Close design guide modal
    $('#si-design-guide-modal .si-modal-close, #si-design-guide-modal').on('click', function(e) {
        if (e.target === this) {
            $('#si-design-guide-modal').fadeOut(300);
        }
    });

    // Prevent modal content clicks from closing modal
    $('#si-design-guide-modal .si-modal-content').on('click', function(e) {
        e.stopPropagation();
    });
});
</script>

<!-- Design Guide Modal -->
<div id="si-design-guide-modal" class="si-modal" style="display: none;">
    <div class="si-modal-content si-modal-large">
        <div class="si-modal-header">
            <h2><?php echo esc_html__('Custom Design Guide', 'simple-invoice'); ?></h2>
            <span class="si-modal-close">&times;</span>
        </div>
        <div class="si-modal-body">
            <div class="si-guide-content">
                <h3><?php echo esc_html__('How to Add Custom Designs', 'simple-invoice'); ?></h3>

                <div class="si-guide-step">
                    <h4><span class="si-step-number">1</span> <?php echo esc_html__('Create Design Folder', 'simple-invoice'); ?></h4>
                    <p><?php echo esc_html__('Create a new folder in:', 'simple-invoice'); ?></p>
                    <code>wp-content/plugins/simple-invoice/designs/your-design-name/</code>
                </div>

                <div class="si-guide-step">
                    <h4><span class="si-step-number">2</span> <?php echo esc_html__('Required Files', 'simple-invoice'); ?></h4>
                    <ul>
                        <li><strong>template.php</strong> - <?php echo esc_html__('Main template file (required)', 'simple-invoice'); ?></li>
                        <li><strong>style.css</strong> - <?php echo esc_html__('Custom CSS styles (optional)', 'simple-invoice'); ?></li>
                        <li><strong>preview.jpg</strong> - <?php echo esc_html__('Preview image (optional)', 'simple-invoice'); ?></li>
                    </ul>
                </div>

                <div class="si-guide-step">
                    <h4><span class="si-step-number">3</span> <?php echo esc_html__('Available Variables', 'simple-invoice'); ?></h4>
                    <p><?php echo esc_html__('Use these variables in your template:', 'simple-invoice'); ?></p>
                    <div class="si-variables-grid">
                        <div class="si-variable-group">
                            <h5><?php echo esc_html__('Business Info', 'simple-invoice'); ?></h5>
                            <ul>
                                <li><code>$settings['business_name']</code></li>
                                <li><code>$settings['business_address']</code></li>
                                <li><code>$settings['business_email']</code></li>
                                <li><code>$settings['business_phone']</code></li>
                            </ul>
                        </div>
                        <div class="si-variable-group">
                            <h5><?php echo esc_html__('Invoice Details', 'simple-invoice'); ?></h5>
                            <ul>
                                <li><code>$invoice_number</code></li>
                                <li><code>$invoice_date</code></li>
                                <li><code>$due_date</code></li>
                                <li><code>$notes</code></li>
                            </ul>
                        </div>
                        <div class="si-variable-group">
                            <h5><?php echo esc_html__('Client Info', 'simple-invoice'); ?></h5>
                            <ul>
                                <li><code>$client->name</code></li>
                                <li><code>$client->email</code></li>
                                <li><code>$client->address</code></li>
                                <li><code>$client->contact_number</code></li>
                            </ul>
                        </div>
                        <div class="si-variable-group">
                            <h5><?php echo esc_html__('Calculations', 'simple-invoice'); ?></h5>
                            <ul>
                                <li><code>$items</code> - <?php echo esc_html__('Array of items', 'simple-invoice'); ?></li>
                                <li><code>$subtotal</code></li>
                                <li><code>$tax_amount</code></li>
                                <li><code>$total_amount</code></li>
                            </ul>
                        </div>
                    </div>
                </div>

                <div class="si-guide-step">
                    <h4><span class="si-step-number">4</span> <?php echo esc_html__('Quick Start', 'simple-invoice'); ?></h4>
                    <ol>
                        <li><?php echo esc_html__('Copy an existing design folder (e.g., "classic")', 'simple-invoice'); ?></li>
                        <li><?php echo esc_html__('Rename it to your design name', 'simple-invoice'); ?></li>
                        <li><?php echo esc_html__('Modify template.php with your custom HTML/CSS', 'simple-invoice'); ?></li>
                        <li><?php echo esc_html__('Refresh this page to see your new design', 'simple-invoice'); ?></li>
                    </ol>
                </div>

                <div class="si-guide-actions">
                    <a href="<?php echo esc_url(WIMP_PLUGIN_URL . 'DESIGN-TEMPLATES-GUIDE.md'); ?>" target="_blank" class="si-btn si-btn-primary">
                        <span class="dashicons dashicons-external"></span>
                        <?php echo esc_html__('View Full Documentation', 'simple-invoice'); ?>
                    </a>
                    <a href="<?php echo esc_url(WIMP_PLUGIN_URL . 'designs/classic/template.php'); ?>" target="_blank" class="si-btn si-btn-secondary">
                        <span class="dashicons dashicons-media-code"></span>
                        <?php echo esc_html__('View Example Template', 'simple-invoice'); ?>
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>

<?php
// Include common footer
include WIMP_PLUGIN_PATH . 'admin/views/common/page-footer.php';
?>
