/* WP Invoice Manager Pro - Professional Design System */
:root {
  --primary-color: #f47a45;
  --secondary-color: #5f5f5f;
  --background-color: #ffffff;
  --background-alt-color: #f9fafb;
  --light-border-color: #e7e7e7;
  --text-main-color: #000000;

  /* Derived colors for consistency */
  --primary-hover: #e06a35;
  --primary-light: rgba(244, 122, 69, 0.1);
  --secondary-light: rgba(95, 95, 95, 0.1);
  --text-muted: var(--secondary-color);
  --border-radius: 8px;
  --border-radius-sm: 5px;
  --shadow-sm: 0 2px 4px rgba(0, 0, 0, 0.1);
  --shadow-md: 0 4px 8px rgba(0, 0, 0, 0.12);
  --transition: all 0.3s ease;
}

/* Global Styles */
body {
  font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", <PERSON><PERSON>, "Helvetica Neue", <PERSON><PERSON>, sans-serif;
  font-size: 14px;
  line-height: 1.6;
  color: var(--text-main-color);
  background-color: var(--background-alt-color);
}

h1, h2, h3, h4, h5, h6 {
  color: var(--text-main-color);
  font-weight: 600;
  margin: 0 0 1rem 0;
}

/* Container and Layout */
.si-page-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 20px;
}

.si-page-content {
  background-color: var(--background-color);
  border-radius: var(--border-radius);
  box-shadow: var(--shadow-sm);
  padding: 30px;
  margin-top: 20px;
}

/* Page Header System */
.si-page-header {
  background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-hover) 100%);
  color: var(--background-color);
  padding: 30px;
  margin-bottom: 30px;
  border-radius: var(--border-radius);
  box-shadow: var(--shadow-md);
  position: relative;
  overflow: hidden;
}

.si-page-header::before {
  content: '';
  position: absolute;
  top: 0;
  right: 0;
  width: 100px;
  height: 100px;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 50%;
  transform: translate(30px, -30px);
}

.si-header-content {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  position: relative;
  z-index: 1;
}

.si-header-main {
  display: flex;
  align-items: center;
  gap: 15px;
}

.si-header-icon {
  font-size: 32px;
  opacity: 0.9;
}

.si-header-text h1,
.si-page-title {
  font-size: 28px;
  font-weight: 700;
  margin-bottom: 8px;
  color: inherit;
}

.si-header-text p,
.si-page-subtitle {
  font-size: 16px;
  opacity: 0.85;
  margin: 0;
}

.si-header-actions {
  display: flex;
  gap: 10px;
  align-items: center;
}

/* Statistics and Cards */
.si-quick-stats,
.si-stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 20px;
  margin-bottom: 30px;
}

.si-stat-item,
.si-stat-card {
  background-color: var(--background-color);
  border: 1px solid var(--light-border-color);
  padding: 24px;
  text-align: left;
  border-radius: var(--border-radius);
  box-shadow: var(--shadow-sm);
  transition: var(--transition);
  position: relative;
  overflow: hidden;
}

.si-stat-item:hover,
.si-stat-card:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-md);
  border-color: var(--primary-color);
}

.si-stat-card {
  display: flex;
  align-items: center;
  gap: 16px;
}

.si-stat-icon {
  width: 48px;
  height: 48px;
  background-color: var(--primary-light);
  border-radius: var(--border-radius-sm);
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--primary-color);
  font-size: 24px;
  flex-shrink: 0;
}

.si-stat-content {
  flex: 1;
}

.si-stat-number {
  font-size: 28px;
  font-weight: 700;
  margin-bottom: 4px;
  color: var(--text-main-color);
}

.si-stat-label {
  font-size: 14px;
  color: var(--text-muted);
  font-weight: 500;
  margin-bottom: 2px;
}

.si-stat-meta {
  font-size: 12px;
  color: var(--text-muted);
  opacity: 0.8;
}

/* Button System */
.si-btn {
  display: inline-flex;
  align-items: center;
  gap: 8px;
  padding: 12px 20px;
  border: none;
  border-radius: var(--border-radius-sm);
  cursor: pointer;
  font-size: 14px;
  font-weight: 500;
  text-decoration: none;
  transition: var(--transition);
  position: relative;
  overflow: hidden;
}

/* Primary Button */
.si-btn-primary,
.si-btn {
  background-color: var(--primary-color);
  color: var(--background-color);
}

.si-btn-primary:hover,
.si-btn:hover {
  background-color: var(--primary-hover);
  transform: translateY(-1px);
  box-shadow: var(--shadow-sm);
}

/* Secondary Button */
.si-btn-secondary {
  background-color: var(--background-color);
  color: var(--secondary-color);
  border: 1px solid var(--light-border-color);
}

.si-btn-secondary:hover {
  background-color: var(--background-alt-color);
  border-color: var(--secondary-color);
  color: var(--text-main-color);
}

/* Ghost Button */
.si-btn-ghost {
  background-color: transparent;
  color: var(--secondary-color);
  border: 1px solid var(--light-border-color);
}

.si-btn-ghost:hover {
  background-color: var(--secondary-light);
  border-color: var(--secondary-color);
  color: var(--text-main-color);
}

/* Large Button */
.si-btn-large {
  padding: 16px 32px;
  font-size: 16px;
  font-weight: 600;
}

/* Button States */
.si-btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  transform: none;
}

.si-btn:disabled:hover {
  transform: none;
  box-shadow: none;
}

/* Search Section */
.si-search-section {
  margin-bottom: 20px;
  background-color: var(--background-color);
  padding: 20px;
  border: 1px solid var(--light-border-color);
  border-radius: var(--border-radius);
  box-shadow: var(--shadow-sm);
}

.si-search-input {
  width: 100%;
  padding: 12px 16px;
  border: 1px solid var(--light-border-color);
  border-radius: var(--border-radius-sm);
  font-size: 14px;
  transition: var(--transition);
}

.si-search-input:focus {
  outline: none;
  border-color: var(--primary-color);
  box-shadow: 0 0 0 3px var(--primary-light);
}

/* Form System */
.si-form-section {
  background-color: var(--background-color);
  border: 1px solid var(--light-border-color);
  border-radius: var(--border-radius);
  margin-bottom: 24px;
  overflow: hidden;
  box-shadow: var(--shadow-sm);
}

.si-section-header {
  background-color: var(--background-alt-color);
  padding: 20px 24px;
  border-bottom: 1px solid var(--light-border-color);
}

.si-section-header h2 {
  display: flex;
  align-items: center;
  gap: 10px;
  margin: 0 0 8px 0;
  font-size: 18px;
  font-weight: 600;
  color: var(--text-main-color);
}

.si-section-description {
  margin: 0;
  color: var(--text-muted);
  font-size: 14px;
}

.si-form-grid {
  display: grid;
  gap: 20px;
  padding: 24px;
}

.si-grid-2 {
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
}

.si-form-field {
  display: flex;
  flex-direction: column;
}

.si-field-label,
.si-form-label {
  display: flex;
  align-items: center;
  gap: 8px;
  font-weight: 600;
  margin-bottom: 8px;
  color: var(--text-main-color);
  font-size: 14px;
}

.si-field-label .required {
  color: var(--primary-color);
}

.si-field-wrapper {
  position: relative;
}

.si-field-help {
  font-size: 12px;
  color: var(--text-muted);
  margin-top: 4px;
  display: block;
}

/* Form Input Elements */
.si-input,
.si-select,
.si-textarea,
.si-form-input,
.si-form-textarea {
  width: 100%;
  padding: 12px 16px;
  border: 1px solid var(--light-border-color);
  border-radius: var(--border-radius-sm);
  font-size: 14px;
  font-family: inherit;
  transition: var(--transition);
  background-color: var(--background-color);
  color: var(--text-main-color);
}

.si-input:focus,
.si-select:focus,
.si-textarea:focus,
.si-form-input:focus,
.si-form-textarea:focus {
  outline: none;
  border-color: var(--primary-color);
  box-shadow: 0 0 0 3px var(--primary-light);
}

.si-textarea {
  resize: vertical;
  min-height: 100px;
}

.si-select {
  cursor: pointer;
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%236b7280' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='m6 8 4 4 4-4'/%3e%3c/svg%3e");
  background-position: right 12px center;
  background-repeat: no-repeat;
  background-size: 16px;
  padding-right: 40px;
}

/* Input Groups */
.si-client-select-wrapper {
  display: flex;
  gap: 8px;
  align-items: stretch;
}

.si-client-select-wrapper .si-select {
  flex: 1;
}

.si-calc-input-wrapper {
  position: relative;
  display: flex;
  align-items: center;
}

.si-input-prefix,
.si-input-suffix {
  position: absolute;
  color: var(--text-muted);
  font-size: 14px;
  font-weight: 500;
}

.si-input-prefix {
  left: 12px;
}

.si-input-suffix {
  right: 12px;
}

.si-calc-input-wrapper .si-calc-input {
  padding-left: 32px;
  padding-right: 32px;
}

/* Tables */
.si-table,
.si-invoice-items-table {
  width: 100%;
  border-collapse: collapse;
  border-spacing: 0;
  background-color: var(--background-color);
  border-radius: var(--border-radius);
  overflow: hidden;
  box-shadow: var(--shadow-sm);
}

.si-table th,
.si-table td,
.si-invoice-items-table th,
.si-invoice-items-table td {
  padding: 16px;
  text-align: left;
  border-bottom: 1px solid var(--light-border-color);
}

.si-table th,
.si-invoice-items-table th {
  background-color: var(--background-alt-color);
  font-weight: 600;
  color: var(--text-main-color);
  font-size: 14px;
}

.si-table tbody tr:hover,
.si-invoice-items-table tbody tr:hover {
  background-color: var(--background-alt-color);
}

/* Modal System */
.si-modal {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.6);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 10000;
  backdrop-filter: blur(2px);
}

.si-modal-content {
  background-color: var(--background-color);
  border-radius: var(--border-radius);
  box-shadow: var(--shadow-md);
  max-width: 600px;
  width: 90%;
  max-height: 90vh;
  overflow: hidden;
  display: flex;
  flex-direction: column;
}

.si-modal-large .si-modal-content {
  max-width: 900px;
}

.si-modal-header {
  padding: 24px;
  border-bottom: 1px solid var(--light-border-color);
  background-color: var(--background-alt-color);
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.si-modal-header h2 {
  margin: 0;
  font-size: 20px;
  font-weight: 600;
  color: var(--text-main-color);
}

.si-modal-close {
  background: none;
  border: none;
  font-size: 24px;
  cursor: pointer;
  color: var(--text-muted);
  padding: 4px;
  border-radius: var(--border-radius-sm);
  transition: var(--transition);
}

.si-modal-close:hover {
  background-color: var(--light-border-color);
  color: var(--text-main-color);
}

.si-modal-body {
  padding: 24px;
  flex: 1;
  overflow-y: auto;
}

.si-modal-footer {
  padding: 20px 24px;
  border-top: 1px solid var(--light-border-color);
  background-color: var(--background-alt-color);
  display: flex;
  justify-content: flex-end;
  gap: 12px;
}

/* Dashboard Specific Components */
.si-dashboard-stats,
.si-dashboard-breakdown {
  margin-bottom: 32px;
}

.si-dashboard-stats h2,
.si-dashboard-breakdown h2 {
  margin-bottom: 20px;
  font-size: 20px;
  font-weight: 600;
  color: var(--text-main-color);
}

/* Status Cards */
.si-status-stats {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 16px;
}

.si-status-card {
  background-color: var(--background-color);
  border: 1px solid var(--light-border-color);
  border-radius: var(--border-radius);
  padding: 20px;
  text-align: center;
  transition: var(--transition);
  box-shadow: var(--shadow-sm);
}

.si-status-card:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-md);
}

.si-status-number {
  font-size: 32px;
  font-weight: 700;
  margin-bottom: 8px;
  color: var(--text-main-color);
}

.si-status-label {
  font-size: 14px;
  color: var(--text-muted);
  font-weight: 500;
}

/* Status-specific colors using border accents */
.si-status-paid {
  border-left: 4px solid var(--primary-color);
}

.si-status-sent {
  border-left: 4px solid var(--secondary-color);
}

.si-status-draft {
  border-left: 4px solid var(--light-border-color);
}

.si-status-overdue {
  border-left: 4px solid var(--primary-hover);
}

/* Dashboard Columns */
.si-dashboard-columns {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
  gap: 24px;
  margin-top: 32px;
}

.si-dashboard-widget {
  background-color: var(--background-color);
  border: 1px solid var(--light-border-color);
  border-radius: var(--border-radius);
  box-shadow: var(--shadow-sm);
  overflow: hidden;
}

.si-widget-header {
  background-color: var(--background-alt-color);
  padding: 20px 24px;
  border-bottom: 1px solid var(--light-border-color);
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.si-widget-header h3 {
  margin: 0;
  font-size: 16px;
  font-weight: 600;
  color: var(--text-main-color);
}

.si-widget-action {
  color: var(--primary-color);
  text-decoration: none;
  font-size: 14px;
  font-weight: 500;
  transition: var(--transition);
}

.si-widget-action:hover {
  color: var(--primary-hover);
}

.si-widget-content {
  padding: 24px;
}

/* Recent Invoices */
.si-recent-invoices {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.si-recent-invoice {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px;
  background-color: var(--background-alt-color);
  border-radius: var(--border-radius-sm);
  border: 1px solid var(--light-border-color);
  transition: var(--transition);
}

.si-recent-invoice:hover {
  background-color: var(--background-color);
  border-color: var(--primary-color);
}

.si-invoice-info {
  flex: 1;
}

.si-invoice-number {
  font-weight: 600;
  color: var(--text-main-color);
  margin-bottom: 4px;
}

.si-invoice-client {
  color: var(--text-muted);
  font-size: 14px;
  margin-bottom: 2px;
}

.si-invoice-date {
  color: var(--text-muted);
  font-size: 12px;
}

.si-invoice-amount {
  text-align: right;
}

.si-amount {
  font-weight: 600;
  color: var(--text-main-color);
  font-size: 16px;
  margin-bottom: 4px;
}

.si-status-badge {
  display: inline-block;
  padding: 4px 8px;
  border-radius: var(--border-radius-sm);
  font-size: 12px;
  font-weight: 500;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.si-status-badge.si-status-paid {
  background-color: var(--primary-light);
  color: var(--primary-color);
}

.si-status-badge.si-status-sent {
  background-color: var(--secondary-light);
  color: var(--secondary-color);
}

.si-status-badge.si-status-draft {
  background-color: var(--light-border-color);
  color: var(--text-muted);
}

.si-status-badge.si-status-overdue {
  background-color: rgba(244, 122, 69, 0.2);
  color: var(--primary-hover);
}

/* Quick Actions */
.si-quick-actions {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.si-quick-action {
  display: flex;
  align-items: center;
  gap: 16px;
  padding: 16px;
  background-color: var(--background-alt-color);
  border: 1px solid var(--light-border-color);
  border-radius: var(--border-radius-sm);
  text-decoration: none;
  color: var(--text-main-color);
  transition: var(--transition);
}

.si-quick-action:hover {
  background-color: var(--background-color);
  border-color: var(--primary-color);
  transform: translateX(4px);
}

.si-action-icon {
  width: 40px;
  height: 40px;
  background-color: var(--primary-light);
  border-radius: var(--border-radius-sm);
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--primary-color);
  font-size: 20px;
  flex-shrink: 0;
}

.si-action-content {
  flex: 1;
}

.si-action-title {
  font-weight: 600;
  color: var(--text-main-color);
  margin-bottom: 4px;
}

.si-action-desc {
  color: var(--text-muted);
  font-size: 14px;
}

/* Empty States */
.si-empty-state {
  text-align: center;
  padding: 60px 40px;
  background-color: var(--background-color);
  border: 1px solid var(--light-border-color);
  border-radius: var(--border-radius);
  margin: 20px 0;
}

.si-warning-state {
  border-color: var(--primary-color);
  background-color: var(--primary-light);
}

.si-empty-icon {
  font-size: 48px;
  color: var(--text-muted);
  margin-bottom: 16px;
}

.si-warning-state .si-empty-icon {
  color: var(--primary-color);
}

.si-empty-state h3 {
  margin-bottom: 12px;
  color: var(--text-main-color);
}

.si-empty-state p {
  color: var(--text-muted);
  margin-bottom: 24px;
  max-width: 500px;
  margin-left: auto;
  margin-right: auto;
}

.si-help-note {
  background-color: var(--background-color);
  border: 1px solid var(--light-border-color);
  border-radius: var(--border-radius-sm);
  padding: 16px;
  margin: 16px 0 24px 0;
  text-align: left;
  max-width: 400px;
  margin-left: auto;
  margin-right: auto;
}

.si-help-note strong {
  color: var(--text-main-color);
}

.si-help-note span {
  color: var(--text-muted);
  font-size: 14px;
}

/* Invoice Creation Form */
.si-create-invoice-wrap {
  max-width: 1200px;
  margin: 0 auto;
}

.si-modern-form {
  display: flex;
  flex-direction: column;
  gap: 24px;
}

/* Template Preview */
.si-template-preview {
  margin-top: 12px;
  background-color: var(--background-alt-color);
  border: 1px solid var(--light-border-color);
  border-radius: var(--border-radius-sm);
  padding: 16px;
}

.si-preview-header {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 12px;
  color: var(--text-muted);
  font-size: 14px;
  font-weight: 500;
}

.si-preview-content {
  display: flex;
  align-items: center;
  gap: 12px;
}

.si-design-badge {
  display: inline-block;
  padding: 4px 8px;
  background-color: var(--primary-light);
  color: var(--primary-color);
  border-radius: var(--border-radius-sm);
  font-size: 12px;
  font-weight: 500;
  text-transform: uppercase;
}

.si-preview-info h4 {
  margin: 0 0 4px 0;
  color: var(--text-main-color);
  font-size: 14px;
}

.si-preview-info p {
  margin: 0;
  color: var(--text-muted);
  font-size: 12px;
}

/* Invoice Items Table */
.si-items-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
  padding: 0 24px;
}

.si-items-header h3 {
  margin: 0;
  font-size: 16px;
  font-weight: 600;
  color: var(--text-main-color);
}

.si-items-table-container {
  padding: 0 24px 24px 24px;
}

.si-invoice-items-table {
  margin-bottom: 16px;
}

.si-items-empty {
  text-align: center;
  padding: 40px 20px;
  background-color: var(--background-alt-color);
  border: 2px dashed var(--light-border-color);
  border-radius: var(--border-radius);
}

.si-items-empty .si-empty-icon {
  font-size: 32px;
  color: var(--text-muted);
  margin-bottom: 12px;
}

.si-items-empty h4 {
  margin-bottom: 8px;
  color: var(--text-main-color);
}

.si-items-empty p {
  color: var(--text-muted);
  margin: 0;
}

/* Table Column Widths */
.si-col-sr { width: 60px; }
.si-col-description { width: auto; }
.si-col-quantity { width: 100px; }
.si-col-rate { width: 120px; }
.si-col-total { width: 120px; }
.si-col-actions { width: 80px; }

/* Invoice Summary */
.si-summary-container {
  display: grid;
  grid-template-columns: 1fr 300px;
  gap: 32px;
  padding: 24px;
}

.si-summary-calculations {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.si-calc-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 0;
  border-bottom: 1px solid var(--light-border-color);
}

.si-calc-row:last-child {
  border-bottom: none;
}

.si-calc-label {
  display: flex;
  align-items: center;
  gap: 8px;
  font-weight: 500;
  color: var(--text-main-color);
}

.si-calc-value {
  font-weight: 600;
  color: var(--text-main-color);
  font-size: 16px;
}

.si-calc-input-row .si-calc-input {
  width: 120px;
  text-align: right;
}

/* Total Card */
.si-total-card {
  background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-hover) 100%);
  color: var(--background-color);
  border-radius: var(--border-radius);
  padding: 24px;
  text-align: center;
  box-shadow: var(--shadow-md);
}

.si-total-header h3 {
  margin: 0 0 16px 0;
  font-size: 16px;
  font-weight: 500;
  opacity: 0.9;
}

.si-total-amount-display {
  margin-bottom: 16px;
}

.si-currency {
  font-size: 24px;
  font-weight: 300;
  opacity: 0.8;
}

.si-amount {
  font-size: 36px;
  font-weight: 700;
  margin-left: 4px;
}

.si-total-label {
  font-size: 14px;
  opacity: 0.8;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

/* Notes Section */
.si-notes-wrapper {
  padding: 24px;
}

.si-notes-help {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-top: 8px;
  color: var(--text-muted);
  font-size: 12px;
}

/* Form Actions */
.si-form-actions {
  background-color: var(--background-alt-color);
  border: 1px solid var(--light-border-color);
  border-radius: var(--border-radius);
  padding: 24px;
  margin-top: 32px;
  box-shadow: var(--shadow-sm);
}

.si-actions-container {
  display: flex;
  justify-content: space-between;
  align-items: center;
  gap: 16px;
}

.si-actions-left,
.si-actions-right {
  display: flex;
  gap: 12px;
  align-items: center;
}

/* Item Row Styles */
.si-item-row {
  background-color: var(--background-color);
}

.si-item-row:hover {
  background-color: var(--background-alt-color);
}

.si-item-input {
  border: none;
  background: transparent;
  padding: 8px;
  width: 100%;
  font-size: 14px;
  color: var(--text-main-color);
}

.si-item-input:focus {
  outline: 1px solid var(--primary-color);
  border-radius: var(--border-radius-sm);
  background-color: var(--background-color);
}

.si-remove-item {
  background: none;
  border: none;
  color: var(--text-muted);
  cursor: pointer;
  padding: 8px;
  border-radius: var(--border-radius-sm);
  transition: var(--transition);
}

.si-remove-item:hover {
  background-color: rgba(244, 122, 69, 0.1);
  color: var(--primary-color);
}

/* WordPress Admin Integration */
.wrap.si-create-invoice-wrap {
  margin: 0;
  padding: 0;
}

/* Form Table Compatibility */
.form-table th {
  color: var(--text-main-color);
  font-weight: 600;
}

.form-table td input[type="text"],
.form-table td input[type="email"],
.form-table td textarea {
  border: 1px solid var(--light-border-color);
  border-radius: var(--border-radius-sm);
  padding: 8px 12px;
}

.form-table td input[type="text"]:focus,
.form-table td input[type="email"]:focus,
.form-table td textarea:focus {
  border-color: var(--primary-color);
  box-shadow: 0 0 0 2px var(--primary-light);
  outline: none;
}

/* WordPress Button Overrides */
.button-primary {
  background-color: var(--primary-color) !important;
  border-color: var(--primary-color) !important;
  color: var(--background-color) !important;
}

.button-primary:hover {
  background-color: var(--primary-hover) !important;
  border-color: var(--primary-hover) !important;
}

.button-secondary {
  background-color: var(--background-color) !important;
  border-color: var(--light-border-color) !important;
  color: var(--text-main-color) !important;
}

.button-secondary:hover {
  background-color: var(--background-alt-color) !important;
  border-color: var(--secondary-color) !important;
}

/* Responsive Design */
@media (max-width: 768px) {
  .si-page-container {
    padding: 16px;
  }

  .si-page-content {
    padding: 20px;
  }

  .si-header-content {
    flex-direction: column;
    gap: 16px;
    align-items: flex-start;
  }

  .si-header-main {
    flex-direction: column;
    align-items: flex-start;
    gap: 8px;
  }

  .si-header-actions {
    width: 100%;
    justify-content: flex-start;
  }

  .si-stats-grid,
  .si-quick-stats {
    grid-template-columns: 1fr;
  }

  .si-form-grid.si-grid-2 {
    grid-template-columns: 1fr;
  }

  .si-summary-container {
    grid-template-columns: 1fr;
    gap: 20px;
  }

  .si-dashboard-columns {
    grid-template-columns: 1fr;
  }

  .si-actions-container {
    flex-direction: column;
    align-items: stretch;
  }

  .si-actions-left,
  .si-actions-right {
    justify-content: center;
  }

  .si-modal-content {
    width: 95%;
    margin: 20px;
  }

  .si-client-select-wrapper {
    flex-direction: column;
  }
}

@media (max-width: 480px) {
  .si-page-header {
    padding: 20px;
  }

  .si-header-text h1,
  .si-page-title {
    font-size: 24px;
  }

  .si-stat-card {
    flex-direction: column;
    text-align: center;
    gap: 12px;
  }

  .si-form-section {
    margin-bottom: 16px;
  }

  .si-section-header {
    padding: 16px 20px;
  }

  .si-form-grid {
    padding: 20px;
  }

  .si-items-table-container {
    padding: 0 16px 16px 16px;
    overflow-x: auto;
  }

  .si-invoice-items-table {
    min-width: 600px;
  }
}
