/* General Styles */
:root {
  --primary-color: #f47a45;
  --secondary-color: #5f5f5f;
  --background-color: #ffffff;
  --background-alt-color: #f9fafb;
  --light-border-color: #e7e7e7;
  --text-main-color: #000000;
}

body {
  font-family: sans-serif;
  font-size: 14px;
  line-height: 1.6;
  color: var(--text-main-color);
  background-color: var(--background-alt-color);
}

h1, h2, h3, h4, h5, h6 {
    color: var(--text-main-color);
}

/* Page Header */
.si-page-header {
  background-color: var(--primary-color);
  color: var(--background-color);
  padding: 30px;
  margin-bottom: 30px;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.si-page-title {
  font-size: 28px;
  font-weight: 700;
  margin-bottom: 10px;
}

.si-page-subtitle {
  font-size: 16px;
  opacity: 0.8;
}

/* Quick Stats */
.si-quick-stats {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 20px;
  margin-bottom: 30px;
}

.si-stat-item {
  background-color: var(--background-color);
  border: 1px solid var(--light-border-color);
  padding: 20px;
  text-align: left;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.si-stat-number {
  font-size: 24px;
  font-weight: 700;
  margin-bottom: 5px;
}

.si-stat-label {
  font-size: 14px;
  color: var(--secondary-color);
}

/* Search Section */
.si-search-section {
  margin-bottom: 20px;
  background-color: var(--background-color);
  padding: 20px;
  border: 1px solid var(--light-border-color);
  border-radius: 8px;
}

.si-search-input {
  width: 100%;
  padding: 10px;
  border: 1px solid var(--light-border-color);
  border-radius: 5px;
}

.si-search-input:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 5px rgba(var(--primary-color-rgb), 0.5);
}

/* Buttons */
.si-btn {
  background-color: var(--primary-color);
  color: var(--background-color);
  padding: 10px 20px;
  border: none;
  border-radius: 5px;
  cursor: pointer;
  transition: background-color 0.3s ease;
}

.si-btn:hover {
  background-color: var(--secondary-color);
}

/* Tables */
.si-table {
  width: 100%;
  border-collapse: collapse;
  border-spacing: 0;
}

.si-table th,
.si-table td {
  padding: 12px 15px;
  text-align: left;
  border-bottom: 1px solid var(--light-border-color);
}

.si-table th {
  font-weight: bold;
  color: var(--secondary-color);
}

/* Forms */
.si-form-group {
  margin-bottom: 15px;
}

.si-form-label {
  display: block;
  font-weight: bold;
  margin-bottom: 5px;
  color: var(--secondary-color);
}

.si-form-input,
.si-form-textarea {
  width: 100%;
  padding: 10px;
  border: 1px solid var(--light-border-color);
  border-radius: 5px;
}

.si-form-input:focus,
.si-form-textarea:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 5px rgba(var(--primary-color-rgb), 0.5);
}

/* Modals */
.si-modal {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
}

.si-modal-content {
  background-color: var(--background-color);
  padding: 20px;
  border-radius: 5px;
}
